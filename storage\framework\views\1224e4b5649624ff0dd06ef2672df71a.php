<?php $__env->startSection('content'); ?>
    <section class="service padding">
        <div class="container">
            <div class="row row-gap-10">
                <div class="col-md-12 d-flex justify-content-between">
                    <h4 class="sora black">Services</h4>
                    <div class="search-bar d-flex align-items-center">
                        <i class="fa-solid fa-magnifying-glass me-3"></i>
                        <input class="search-servies" type="text" placeholder="Search" name="search-servies">
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="position-relative">
                        <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">

                            <!-- Swiper -->
                            <div class="swiper mySwiper professional-swipper ">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link  professional-tab" id="pills-home-tab"
                                                data-bs-toggle="pill" data-bs-target="#pills-home" type="button"
                                                role="tab" aria-controls="pills-home" aria-selected="true">Personal
                                                Trainers
                                            </button>
                                        </li>
                                    </div>
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="swiper-slide">
                                            <li class="nav-item" role="presentation">
                                                <button
                                                    class="nav-link <?php echo e($category->slug == $active_category ? 'active' : ''); ?> professional-tab category-tab-btn"
                                                    id="pills-profile-tab" data-category-name="<?php echo e($category->slug); ?>"
                                                    data-bs-toggle="pill" data-bs-target="#pills-profile" type="button"
                                                    role="tab" aria-controls="pills-profile" aria-selected="false"
                                                    href="#services-makeup-artist-subcategory-1"> <?php echo e($category->name); ?>

                                                </button>
                                            </li>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <div class="swiper-button-next"></div>
                                <div class="swiper-button-prev"></div>
                            </div>
                        </ul>
                    </div>
                    <hr class="mt-0 mb-2">

                    <div id="subcategory-service-container"></div>
                </div>
            </div>
        </div>
    </section>

    <?php echo $__env->make('website.template.modal.filter-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css'); ?>
    <style>
        .services-loader {
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .subcategory-tab-btn {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .subcategory-tab-btn:hover {
            background-color: #f8f9fa;
        }

        .subcategory-tab-btn.active {
            background-color: #007bff;
            color: white;
        }

        .no-services-found {
            padding: 2rem;
        }

        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function() {
            // Category tab click handler
            $('.category-tab-btn').on('click', function() {
                let category = $(this).data("category-name");

                // Remove active class from all category tabs
                $('.category-tab-btn').removeClass('active');
                $(this).addClass('active');

                // Show loading state
                showLoader();

                // Update URL to category only (first subcategory will be auto-selected)
                let newUrl = `/services-all/${category}`;
                window.history.pushState({path: newUrl}, '', newUrl);

                // Load category with subcategories
                $.ajax({
                    url: "<?php echo e(route('filter_services')); ?>",
                    type: "GET",
                    data: { category: category },
                    success: function(response) {
                        hideLoader();
                        if(response.status == true) {
                            $('#subcategory-service-container').html(response.data);
                            // Bind subcategory click events after loading
                            bindSubcategoryEvents();
                            // Auto-load first subcategory services
                            loadFirstSubcategoryServices();
                        } else {
                            $('#subcategory-service-container').html('<div class="alert alert-danger">Something went wrong</div>');
                        }
                    },
                    error: function() {
                        hideLoader();
                        $('#subcategory-service-container').html('<div class="alert alert-danger">Failed to load services</div>');
                    }
                });
            });

            // Function to bind subcategory events
            function bindSubcategoryEvents() {
                $(document).off('click', '.subcategory-tab-btn').on('click', '.subcategory-tab-btn', function() {
                    let subcategorySlug = $(this).data('subcategory-slug');
                    let subcategoryName = $(this).data('subcategory-name');

                    // Remove active class from all subcategory tabs
                    $('.subcategory-tab-btn').removeClass('active');
                    $(this).addClass('active');

                    // Update URL with subcategory
                    let currentCategory = $('.category-tab-btn.active').data('category-name');
                    let newUrl = `/services-all/${currentCategory}/${subcategorySlug}`;
                    window.history.pushState({path: newUrl}, '', newUrl);

                    // Load services for this subcategory
                    loadSubcategoryServices(subcategorySlug);
                });
            }

            // Function to load services by subcategory
            function loadSubcategoryServices(subcategorySlug) {
                showServicesLoader();

                $.ajax({
                    url: "<?php echo e(route('filter_services_by_subcategory')); ?>",
                    type: "GET",
                    data: { subcategory: subcategorySlug },
                    success: function(response) {
                        hideServicesLoader();
                        if(response.status == true) {
                            $('#services-container').html(response.data).addClass('fade-in');
                            updateServicesCount();
                        } else {
                            $('#services-container').html('<div class="alert alert-danger">No services found</div>');
                        }
                    },
                    error: function() {
                        hideServicesLoader();
                        $('#services-container').html('<div class="alert alert-danger">Failed to load services</div>');
                    }
                });
            }

            // Function to auto-load first subcategory services
            function loadFirstSubcategoryServices() {
                let firstSubcategory = $('.subcategory-tab-btn.active').first();
                if(firstSubcategory.length > 0) {
                    let subcategorySlug = firstSubcategory.data('subcategory-slug');
                    loadSubcategoryServices(subcategorySlug);
                }
            }

            // Function to update services count
            function updateServicesCount() {
                let servicesCount = $('[data-services-count]').data('services-count') || $('#services-container .col-md-3').length;
                $('.services-count-number').text(servicesCount);
            }

            // Loading state functions
            function showLoader() {
                $('#subcategory-service-container').html(`
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading subcategories...</p>
                    </div>
                `);
            }

            function hideLoader() {
                // Loader will be replaced by content
            }

            function showServicesLoader() {
                $('#services-loader').show();
                $('#services-container').hide();
            }

            function hideServicesLoader() {
                $('#services-loader').hide();
                $('#services-container').show();
            }

            // Initialize subcategory events if already loaded
            bindSubcategoryEvents();

            // Auto-load services if we have an active subcategory on page load
            if($('.subcategory-tab-btn.active').length > 0) {
                loadFirstSubcategoryServices();
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\git-file\anders\resources\views/website/service.blade.php ENDPATH**/ ?>