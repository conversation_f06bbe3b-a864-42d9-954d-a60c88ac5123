<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_holidays', function (Blueprint $table) {
            $table->boolean('is_full_day')->default(1)->after('end_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_holidays', function (Blueprint $table) {
            $table->dropColumn('is_full_day');
        });
    }
};
