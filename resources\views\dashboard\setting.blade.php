@extends('dashboard.layout.master')
@push('css')
    <!-- Include Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>

    </style>
@endpush
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard business-setting">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12">
                    <h6 class="sora black">Settings</h6>
                    <p class="fs-14 sora normal light-black">Lorem ipsum dolor sit amet consectetur. </p>
                </div>
            </div>
            <div class="row row-gap-5">
                <div class="col-md-4">
                    <div class="card-box">
                        <ul class="nav nav-pills flex-column setting-nav gap-3" id="pills-tab" role="tablist">

                            @if (Auth::user()->hasAnyRole(['individual', 'business', 'admin', 'professional']))
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active setting-tabs" id="change-password-tab"
                                        data-bs-toggle="pill" data-bs-target="#pills-change-password" type="button"
                                        role="tab" aria-controls="pills-change-password" aria-selected="true">Change
                                        Password
                                    </button>
                                </li>
                                @if (Auth::user()->hasRole(['admin']))
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link setting-tabs" id="account-tab" data-bs-toggle="pill"
                                            data-bs-target="#pills-account" type="button" role="tab"
                                            aria-controls="pills-account" aria-selected="true">Account
                                        </button>
                                    </li>
                                @endif
                            @endif
                            @if (Auth::user()->hasAnyRole(['individual', 'business']))
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link setting-tabs" id="stripe-tab" data-bs-toggle="pill"
                                        data-bs-target="#pills-stripe" type="button" role="tab"
                                        aria-controls="pills-stripe" aria-selected="true">Stripe Configuration
                                    </button>
                                </li>
                            @endif
                        </ul>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-change-password" role="tabpanel"
                            aria-labelledby="change-password-tab" tabindex="0">
                            <div class="card card-box p-0">
                                <form action="{{ route('update.password') }}" method="POST" id="update-password-form">
                                    @csrf
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Change Password</p>
                                        <button type="submit"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Save
                                            Changes</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12 position-relative">
                                                <label for="current-password" class="form-label form-input-labels">Current
                                                    Password</label>
                                                <input type="password"
                                                    class="form-control form-inputs-field password-toggle"
                                                    placeholder="Enter current password" id="current-password"
                                                    name="current_password">
                                                <span toggle=".password-toggle"
                                                    class="fa  fa-eye-slash  field-icon toggle-password"></span>
                                            </div>
                                            <div class="col-md-12 position-relative">
                                                <label for="new-password" class="form-label form-input-labels">New
                                                    Password</label>
                                                <input type="password"
                                                    class="form-control form-inputs-field password-toggle"
                                                    placeholder="Enter new password" id="new-password"
                                                    name="new_password">
                                                <span toggle=".password-toggle"
                                                    class="fa  fa-eye-slash  field-icon toggle-password"></span>
                                            </div>
                                            <div class="col-md-12 position-relative">
                                                <label for="confirm-password" class="form-label form-input-labels">Confirm
                                                    Password</label>
                                                <input type="password"
                                                    class="form-control form-inputs-field password-toggle"
                                                    placeholder="Confirm new password" id="confirm-password"
                                                    name="confirm_password">
                                                <span toggle=".password-toggle"
                                                    class="fa  fa-eye-slash  field-icon toggle-password"></span>
                                            </div>
                                            {{-- <p class="sora light-black fs-14 normal m-0">8 characters or longer. Combine
                                                upper
                                                and lowercase letters and numbers.</p> --}}
                                        </div>
                                    </div>
                                </form>
                            </div>

                        </div>
                        {{-- @if (Auth::user()->hasAnyRole(['individual', 'business']))
                            <div class="tab-pane fade" id="pills-account" role="tabpanel" aria-labelledby="account-tab"
                                tabindex="0">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Personal Info</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-6 ">
                                                <label for="full-name" class="form-label form-input-labels">Full
                                                    name</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter full-name" id="full-name" name="full-name"
                                                    value="Roger Press">
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="email" class="form-label form-input-labels">Email
                                                    Address</label>
                                                <input type="email" class="form-control form-inputs-field"
                                                    placeholder="Enter email address" id="email" name="email"
                                                    value="<EMAIL>" disabled>
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="phone-number" class="form-label form-input-labels">Phone
                                                    Number</label>
                                                <input type="tel" class="form-control form-inputs-field"
                                                    placeholder="Enter phone number " id="phone-number"
                                                    name="phone-number" value="+56-955-588-939">
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="location"
                                                    class="form-label form-input-labels">Location</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter email address" id="location" name="location"
                                                    value="6391 Elgin St. Celina, Delaware 10299">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif --}}

                        @if (Auth::user()->hasRole(['admin']))
                            <div class="tab-pane fade" id="pills-account" role="tabpanel" aria-labelledby="account-tab"
                                tabindex="0">
                                <div class="card">
                                    <div class="card-header align-items-center py-5">
                                        <p class="sora black fs-16 semi_bold">Account</p>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12 ">
                                                <div class="Image-input_holder settings-image-input  mb-10">
                                                    <div class="image-input image-input-empty" data-kt-image-input="true">
                                                        <div class="image-input image-input-outline"
                                                            data-kt-image-input="true">
                                                            <div class="image-input-wrapper w-125px h-125px"></div>
                                                            <label class="dark-green-btn fs-14 regular pt-9"
                                                                data-kt-image-input-action="change">
                                                                <span class="pe-3 fs-14 medium mb-10 deep-blue"> Edit Image
                                                                </span>
                                                                <input type="file" name="avatar"
                                                                    accept=".png, .jpg, .jpeg" />
                                                                <input type="hidden" name="avatar_remove" />
                                                                <p class="fs-14 medium pt-4 light-black"> At least 500x500
                                                                    px
                                                                    recommended.
                                                                    JPG or PNG is allowed</p>
                                                            </label>
                                                            <a href="#!" class="light-green-btn fs-14 regular ms-5"
                                                                data-kt-image-input-action="cancel"
                                                                data-bs-toggle="tooltip" data-bs-dismiss="click"
                                                                title="Cancel avatar"> <i class="fas fa-times fa-5"></i>
                                                            </a>
                                                            <a href="#!" class="light-green-btn fs-14 regular ms-5"
                                                                data-kt-image-input-action="remove"> Remove </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6 ">
                                                <label for="email" class="form-label form-input-labels">Email
                                                    Address</label>
                                                <input type="email" class="form-control form-inputs-field"
                                                    placeholder="Enter email address" id="email" name="email"
                                                    value="<EMAIL>" disabled>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="tab-pane fade" id="pills-stripe" role="tabpanel" aria-labelledby="stripe-tab"
                            tabindex="0">
                            <div class="card card-box p-0">
                                <div class="card-header align-items-center">
                                    <p class="sora black fs-16 semi_bold m-0">Stripe Configuration</p>
                                </div>
                                <div class="card-body">
                                    <div class="row row-gap-8 justify-content-center">
                                        <div class="col-md-6  col-xxl-5 p-0">
                                            <div class="d-flex flex-column justify-content-center gap-3">
                                                <img src="{{ asset('website') }}/assets/images/stripe.svg"
                                                    class="h-100 w-100 object-fit-contain rounded-pill top-rated-image"
                                                    alt="card-image" />
                                                <p class="text-center regular light-black">To start receiving payments for
                                                    your
                                                    sales, you'll need to connect your Stripe account.</p>
                                                <ul class="fs-14 light-black nomal">
                                                    <li>
                                                        Secure and instant payouts directly to your bank
                                                    </li>
                                                    <li>Track and manage your earning seamlessly</li>
                                                    <li>
                                                        Essential for completing sales and withdrawals.</li>
                                                </ul>
                                                <p class="text-center fs-14 normal light-black">Once connected, you'll be
                                                    able to receive payouts for your sales.</p>
                                                <p class="text-center fs-14 normal light-black">⚠️️️️ Important: You won’t
                                                    be able to receive payments until your Stripe account is connected.</p>
                                                <a href="" class="add-btn w-100 text-center">
                                                    Configure Your Stripe
                                                </a>

                                            </div>
                                        </div>
                                        <div class="col-md-12 ">
                                            <div class="card card-box justify-content-center align-items-center">
                                                <p class="fs-16 sora black semi_bold ">How to setup Stripe?</p>

                                                <iframe width="560" height="315"
                                                    src="https://www.youtube.com/embed/WFi9dRhmGFo?si=nQuTSof8_67kxbAK"
                                                    title="YouTube video player" frameborder="0"
                                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; "
                                                    referrerpolicy="strict-origin-when-cross-origin"
                                                    allowfullscreen></iframe>

                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <p
                                                class="form-control form-inputs-field p-0 px-2 d-flex justify-content-between align-items-center">
                                                <img src="{{ asset('website') }}/assets/images/stripe.svg"
                                                    class="h-50px w-50px object-fit-contain" alt="card-image" />
                                                <span class="opacity-6">Account Configured!</span>
                                            </p>
                                            <button type="button" class="add-btn"> Remove Stripe Account</button>
                                            <p class="light-black fs-14 normal my-5"> ⚠️️️️ Important: Once disconnected,
                                                you won't be able to receive payouts for sales until you reconnect a Stripe
                                                account.
                                                If you have pending payments, ensure they are processed before unlinking
                                                your account.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
    <script>
        // Wait for jQuery validate to load
        function waitForValidate() {
            if (typeof $.fn.validate !== 'undefined') {
                console.log('jQuery Validate loaded successfully');
                initializeValidation();
            } else {
                console.log('Waiting for jQuery Validate...');
                setTimeout(waitForValidate, 100);
            }
        }

        function initializeValidation() {
            // Password toggle functionality
            $(".toggle-password").click(function() {
                console.log('Password toggle clicked');
                const input = $(this).siblings("input");
                const inputType = input.attr("type") === "password" ? "text" : "password";
                input.attr("type", inputType);

                $(this).toggleClass("fa-eye-slash fa-eye");
            });

            // Add custom validation method for current password
            $.validator.addMethod("checkCurrentPassword", function(value, element) {
                var isValid = false;
                if (value) {
                    $.ajax({
                        url: "{{ route('check.current.password') }}",
                        type: "POST",
                        async: false,
                        data: {
                            _token: "{{ csrf_token() }}",
                            current_password: value
                        },
                        success: function(response) {
                            isValid = response.valid;
                        },
                        error: function(xhr, status, error) {
                            console.log('Error checking password:', error);
                            isValid = false;
                        }
                    });
                }
                return isValid;
            }, "Current password is incorrect");

            // Form validation
            if (typeof $.fn.validate !== 'undefined') {
                $('#update-password-form').validate({
                rules: {
                    current_password: {
                        required: true,
                        checkCurrentPassword: true
                    },
                    new_password: {
                        required: true,
                        minlength: 8
                    },
                    confirm_password: {
                        required: true,
                        equalTo: "#new-password"
                    }
                },
                messages: {
                    current_password: {
                        required: "Please enter your current password"
                    },
                    new_password: {
                        required: "Please enter your new password",
                        minlength: "Your new password must be at least 8 characters long"
                    },
                    confirm_password: {
                        required: "Please confirm your new password",
                        equalTo: "Please enter the same password as above"
                    }
                },
                errorElement: 'span',
                errorClass: 'text-danger',
                errorPlacement: function(error, element) {
                    error.addClass('invalid-feedback');
                    element.closest('.position-relative').append(error);
                },
                highlight: function(element, errorClass, validClass) {
                    $(element).addClass('is-invalid');
                },
                unhighlight: function(element, errorClass, validClass) {
                    $(element).removeClass('is-invalid');
                },
                submitHandler: function(form) {
                    form.submit();
                }
                });

                // Real-time validation for current password
                $('#current-password').on('blur', function() {
                    $(this).valid();
                });
            } else {
                console.error('jQuery Validate plugin not loaded');
                // Fallback validation
                $('#update-password-form').on('submit', function(e) {
                    var isValid = true;
                    var currentPassword = $('#current-password').val();
                    var newPassword = $('#new-password').val();
                    var confirmPassword = $('#confirm-password').val();

                    if (!currentPassword) {
                        alert('Please enter your current password');
                        isValid = false;
                    }
                    if (!newPassword || newPassword.length < 8) {
                        alert('Please enter a new password with at least 8 characters');
                        isValid = false;
                    }
                    if (newPassword !== confirmPassword) {
                        alert('Passwords do not match');
                        isValid = false;
                    }

                    if (!isValid) {
                        e.preventDefault();
                    }
                });
            }
        }

        $(document).ready(function() {
            console.log('jQuery loaded:', typeof $ !== 'undefined');
            console.log('jQuery validate loaded:', typeof $.fn.validate !== 'undefined');

            // Initialize validation when ready
            waitForValidate();
        });
    </script>
@endpush
