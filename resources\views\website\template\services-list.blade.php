<div class="row row-gap-8" data-services-count="{{ $services->count() }}">
    @forelse ($services as $service)
        <div class="col-md-3">
            <div class="card top-rated-card services-card">
                <div class="card-header border-0 p-0">
                    <img src="{{ asset('website' . '/' . $service->image) }}"
                        class="h-100 w-100 top-rated-image" alt="card-image">
                </div>
                <div class="card-body p-5 bg-white">
                    <p class="fs-16 semi_bold black m-0">{{ $service->name }}</p>
                    <div class="d-flex gap-2 align-items-center">
                        <img src="{{ asset('website') . '/' . ($service?->user?->profile?->avatar ?? 'assets/images/default-avatar.png') }}"
                            class="rounded-pill w-25px h-25px" alt="card-image">
                        <div>
                            <p class="fs-11 semi_bold black m-0">{{ $service?->user?->name ?? 'Professional' }}</p>
                            <p class="fs-10px sora semi_bold m-0 light-black">
                                <i class="fa-solid fa-star" style="color: #000000; font-size: 10px;"></i>5.0
                                <span class="normal deep-blue ms-1">(546)</span>
                                <span class="light-black opacity-6 ms-1">
                                    @include('svg.dot') Al Barsha South, Dubai
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="card-footer border-0 d-flex justify-content-between p-5">
                    <div>
                        <p class="m-0 fs-16 black bold">${{ $service->price }}</p>
                        <p class="m-0 fs-14 regular">
                            <i class="fa-regular fa-clock"></i> {{ $service->duration }} mins
                        </p>
                    </div>
                    <a href="#" class="blue-button">Book Now</a>
                </div>
            </div>
        </div>
    @empty
        <div class="col-12 text-center py-5">
            <div class="no-services-found">
                <i class="fa-solid fa-search fs-48 text-muted mb-3"></i>
                <h5 class="text-muted">No Services Found</h5>
                <p class="text-muted">No services are available in this subcategory at the moment.</p>
            </div>
        </div>
    @endforelse
</div>

<script>
    // Update services count when this template loads
    $(document).ready(function() {
        let count = $('[data-services-count]').data('services-count') || 0;
        $('.services-count-number').text(count);
    });
</script>
