@extends(auth()->check() && auth()->user()->hasRole('customer') ? 'website.layout.master' : 'dashboard.layout.master')
@push('js')
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@push('css')
    <style>
        /* Professional Registration Form Styles */
        .gray-card {
            border-radius: 10px;
            border: 1px solid #DCDDE8;
            background: #FFF;
            padding: 25px;
        }

        .time-picker-calendar label.days {
            display: flex;
            height: 44px;
            gap: 10px;
            align-items: center;
        }

        .upload-box {
            padding: 30px;
            cursor: pointer;
            border-radius: 10px;
            border: 2px dashed #ccc;
            background: #f9f9f9;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            gap: 10px;
            text-align: center;
            transition: background 0.3s ease;
            position: relative;
        }

        .upload-box:hover {
            background-color: #eee;
        }

        .upload-box img {
            width: 60px;
            height: 40px;
        }

        .add-file {
            display: block;
            font-size: 18px;
            margin-top: 10px;
            color: #555;
        }

        .preview-container {
            display: flex;
            flex-wrap: wrap;
            margin-top: 15px;
            gap: 15px;
        }

        .preview-box {
            width: 160px;
            height: 95px;
            border: 1px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
            background-color: #fff;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
        }

        .preview-box img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .remove-image,
        .remove-file {
            position: absolute;
            top: 5px;
            right: 8px;
            background: red;
            color: white;
            border: none;
            border-radius: 50%;
            width: 22px;
            height: 22px;
            font-size: 14px;
            line-height: 20px;
            cursor: pointer;
            text-align: center;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .upload-cert-btn {
            padding: 7px 20px;
            border-radius: 20px;
            background: #020C87;
            color: #FFF;
        }

        .cert-excep {
            display: flex;
            width: 200px;
            align-items: center;
            gap: 8px;
            margin-bottom: 2em;
        }

        .addMoreBtn {
            border-radius: 10px;
            border: 1px dashed #020C87;
            background: #FFF;
            padding: 15px;
            margin-block: 2em;
            width: 100%;
        }

        .start-time .flatpickr-input,
        .end-time .flatpickr-input,
        .start-time1 .flatpickr-input,
        .end-time1 .flatpickr-input {
            border-radius: 10px;
            border: 1px solid #DCDDE8;
            background: #FFF;
            padding: 19px !important;
            width: 120px !important;
        }

        .start-time {
            display: none;
        }

        .blue-btn {
            border-radius: 10px;
            background: #020C87;
            padding: 18px 50px;
            text-align: center;
            color: #FFF;
            border: 1px solid #FFF;
            width: 100%;
        }

        button.delete-block {
            border: 1px solid #FFF;
            background: darkred;
            color: #FFF;
            border-radius: 20px;
            padding: 10px 15px;
        }

        button.add-custom-holiday-btn {
            border-radius: 8px;
            border: 1px solid #F0F0F0;
            padding: 8px 14px;
            color: #020C87;
            font-size: 16px;
            font-weight: 600;
            background: #FFF;
            margin-top: 1em;
        }

        .exception-textarea {
            display: none;
        }

        .exception-checkbox:has(input:checked) .exception-textarea {
            display: block;
        }

        .time-picker-calendar2 {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .time-picker-calendar2 .checked-time {
            display: none;
        }

        .time-picker-calendar2:has(input:checked) .closed-time {
            display: none;
        }

        .time-picker-calendar2:has(input:checked) .checked-time,
        .time-picker-calendar2 .closed-time {
            display: flex;
            align-items: center;
            justify-content: end;
            gap: 10px;
        }

        .fieldlabels {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-border {
            border-top: 1px solid #DCDDE8;
            padding-top: 20px;
            margin-top: 20px;
        }

        .blue-text {
            color: #020C87;
        }

        .HolidayModal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            align-items: center;
            justify-content: center;
        }

        .HolidayModal .modal-content {
            background-color: #fefefe;
            padding: 20px;
            border-radius: 10px;
            width: 400px;
            max-width: 90%;
        }

        .HolidayModal .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .HolidayModal .close:hover {
            color: black;
        }

        .HolidayModal input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #DCDDE8;
            border-radius: 5px;
        }

        .HolidayModal label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        input[type="checkbox"] {
            margin-right: 8px;
        }

        .checkmark {
            font-weight: 500;
        }

        .time-picker-range2 {
            display: flex;
            align-items: center;
        }

        .checked-time {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .closed-time {
            display: flex;
            align-items: center;
        }

        input[type="file"] {
            display: none;
        }

        .preview-item {
            position: relative;
            display: inline-block;
        }

        .remove-preview {
            position: absolute;
            top: -5px;
            right: -5px;
            background: red;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
@endpush
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard profile-setting">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="sora black">Profile</h4>
                        <p class="fs-14 light-black sora">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>

                    @if (auth()->check() && auth()->user()->hasRole('customer'))
                        <div>
                            <button class="drop-btn delete-btn btn btn-outline-danger  py-2 px-3 text-center"><i
                                    class="bi bi-trash p-0 red me-3"></i>Delete Profile
                            </button>
                        </div>
                    @endif
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="card white-box friends-cards ">
                        <div class="card-header align-items-center justify-content-center flex-column gap-3 py-20">

                            <img src="{{ asset('website') . '/' . $user->profile->pic ?? '/website/assets/images/image_input_holder.png' }}"
                                class="customer_profile" alt="card-image" />
                            <p class="fs-22 sora black semi_bold">{{ $user->name ?? '' }}</p>
                        </div>
                        <div class="card-body">
                            <p class="fs-12 normal sora light-black"><span
                                    class="me-3">@include('svg.building')</span>
                                {{ $user->email ?? '' }}</p>
                            @if ($user->profile->country)
                                <p class="fs-12 normal sora light-black"><span
                                        class="me-3">@include('svg.pin')</span>
                                    {{ $user->profile->city ?? '' }}, {{ $user->profile->country ?? '' }}</p>
                            @endif
                            <p class="fs-12 normal sora light-black"></p><span class="me-3"><i
                                    class="fa-regular fa-star"></i></span>No
                            reviews</p>

                            <div class="d-flex gap-4 mt-5">
                                @forelse ($user->socials as $social)
                                    <a href="{{ $social->link }}" target="_blank" class="logo-box">
                                        <img src="{{ asset('website') . '/' . $social->image }}" alt="social-logo"
                                            height="30px" width="30px">
                                    </a>
                                @empty
                                    <p>No Socials Found</p>
                                @endforelse
                            </div>

                            <button type="button" class="blue-button mt-8" data-bs-toggle="modal"
                                data-bs-target="#edit-social-modal">Edit
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="row row-gap-5">
                        @if (Auth::user()->hasAnyRole(['individual', 'business', 'professional']))
                            <!-- businesss and individual -->
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Profile Images</p>
                                        <button type="button" class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal" data-bs-target="#edit-user-galleries-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            @forelse ($user->galleries as $gallery)
                                                <div class="col-md-3 ">
                                                    <img src="{{ asset('website') . '/' . $gallery->image }}"
                                                        class="h-100 w-100 rounded-3  object-fit-contain top-rated-image"
                                                        alt="card-image" />
                                                </div>
                                            @empty
                                                <p>No Images Found</p>
                                            @endforelse
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Personal Info</p>
                                        <button type="button" class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal" data-bs-target="#personal-info-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-6 ">
                                                <label for="full-name" class="form-label form-input-labels">Full
                                                    name</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter full-name" id="full-name" name="full-name"
                                                    value="{{ $user->name ?? '' }}">
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="email" class="form-label form-input-labels">Email
                                                    Address</label>
                                                <input type="email" class="form-control form-inputs-field"
                                                    placeholder="Enter email address" id="email" name="email"
                                                    value="{{ $user->email ?? '' }}" disabled>
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="phone-number" class="form-label form-input-labels">Phone
                                                    Number</label>
                                                <input type="tel" class="form-control form-inputs-field"
                                                    placeholder="Enter phone number " id="phone-number" name="phone-number"
                                                    value="{{ $user->profile->phone ?? '' }}">
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="location" class="form-label form-input-labels">Location</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter email address" id="location" name="location"
                                                    value="{{ $user->profile->location ?? '' }}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Company details</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal"
                                            data-bs-target="#edit-company-details-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12 ">
                                                <label for="company-name" class="form-label form-input-labels">Company
                                                    name</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter company name" id="company-name"
                                                    name="company-name" value="{{ $user->profile->company_name ?? '' }}">
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="company-id" class="form-label form-input-labels">Company
                                                    ID</label>
                                                <input type="email" class="form-control form-inputs-field"
                                                    placeholder="Enter company id" id="company-id" name="company-id"
                                                    value="{{ $user->profile->company_id ?? '' }}">
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="company-vat-number"
                                                    class="form-label form-input-labels">Company VAT
                                                    number</label>
                                                <input type="tel" class="form-control form-inputs-field"
                                                    placeholder="Enter company vat number " id="company-vat-number"
                                                    name="company-vat-number"
                                                    value="{{ $user->profile->vat_number ?? '' }}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Service</p>
                                        {{-- <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button> --}}
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <p class="fs-14 black regular">My Services</p>
                                                <div class="d-flex gap-4 flex-wrap">
                                                    {{-- @forelse ($user->services as $service)
                                                        <p class="fs-14 sora light-black normal service-details">
                                                            {{ $service->name }}
                                                        </p>
                                                        @empty --}}
                                                        <p class="fs-14 sora light-black normal service-details">No
                                                            Services Found</p>
                                                    {{-- @endforelse --}}
                                                </div>
                                            </div>
                                            {{-- <div class="col-md-12">
                                                <p class="fs-14 black regular">Secondary Services</p>
                                                <div class="d-flex gap-4 flex-wrap">
                                                    @for ($i = 0; $i < 6; $i++)
                                                        <p class="fs-14 sora light-black normal service-details">Makeup
                                                            Artists</p>
                                                    @endfor
                                                </div>
                                            </div> --}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Product Certifications</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal"
                                            data-bs-target="#edit-product-certifications-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <div class="d-flex gap-4 flex-wrap">
                                                    @forelse ($user->product_cerficates as $productCertification)
                                                        <p
                                                            class="fs-14 sora light-black normal service-details align-items-center">
                                                            <span>
                                                                <img src="{{ asset('website') . '/' . $productCertification->image }}"
                                                                    class="h-25px w-25px object-fit-contain rounded-pill top-rated-image"
                                                                    alt="card-image" />
                                                            </span>
                                                            <span> {{ $productCertification->name ?? '' }}</span>
                                                        </p>
                                                    @empty
                                                        <p class="fs-14 sora light-black normal service-details">No
                                                            Product Certifications Found</p>
                                                    @endforelse
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Certifications & Licenses</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal"
                                            data-bs-target="#edit-certifications&licenses-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            @forelse ($user->certificates as $certificate)
                                                <div class="col-md-6">
                                                    <div
                                                        class="card card-box flex-row gap-3 justify-content-center align-items-center">
                                                        <div
                                                            class="card-header border-0 card-box justify-content-center align-items-center p-7">
                                                            <img src="{{ asset('website') . '/' . $certificate->image }}"
                                                                class="h-50px w-50px object-fit-contain top-rated-image"
                                                                alt="card-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="black sora fs-16 semi_bold m-0">
                                                                {{ $certificate->title ?? '' }}</p>
                                                            <p class="black fs-14 normal m-0"> <span
                                                                    class="link-gray">Issued by:
                                                                </span>{{ $certificate->issued_by ?? '' }}</p>
                                                            <p class="black fs-14 normal m-0"> <span
                                                                    class="link-gray">Issue
                                                                    Date:</span>{{ $certificate->issued_date ?? '' }}</p>
                                                            <p class="black fs-14 normal m-0"> <span class="link-gray">End
                                                                    Date:
                                                                </span>{{ $certificate->end_date ?? '' }}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            @empty
                                                <p class="fs-14 sora light-black normal service-details">No
                                                    Certifications Found</p>
                                            @endforelse
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Availability</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal" data-bs-target="#edit-availability-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <p class="fs-14 black regular">Weekly Availability </p>
                                                <div class="d-flex gap-2 flex-wrap">
                                                    @forelse ($user->openingHours as $availability)
                                                        <p class="fs-14 sora light-black normal service-details">
                                                            {{ $availability->day }}
                                                            ({{ date('h:i A', strtotime($availability->start_time)) }} -
                                                            {{ date('h:i A', strtotime($availability->end_time)) }})
                                                        </p>
                                                    @empty
                                                        <p class="fs-14 sora light-black normal service-details">No
                                                            Availability Found</p>
                                                    @endforelse
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <p class="fs-14 black regular">Holidays</p>
                                                <div class="d-flex gap-4 flex-wrap">
                                                    @forelse ($user->allHolidays as $holiday)
                                                        <p class="fs-14 sora light-black normal service-details">
                                                            {{ $holiday->name }}
                                                            ({{ $holiday->date }})
                                                        </p>
                                                    @empty
                                                        <p class="fs-14 sora light-black normal service-details">No
                                                            Holidays Found</p>
                                                    @endforelse
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Intro Card</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal" data-bs-target="#intro-cards-modal">Add</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            @forelse ($user->introCards as $introCard)
                                                <div class="col-md-6">
                                                    <div
                                                        class="card card-box p-0 flex-row justify-content-center align-items-center p-3 gap-5">
                                                        <div
                                                            class="card-header border-0 p-0 justify-content-center align-items-center">
                                                            <img src="{{ asset('website') . '/' . $introCard->image }}"
                                                                class="h-35px w-35px  object-fit-contain top-rated-image"
                                                                alt="card-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora w-700 m-0 dark-blue">
                                                                {{ $introCard->heading ?? '' }}</p>
                                                            <p class="fs-14 sora normal  m-0 light-gray">
                                                                {{ $introCard->description ?? '' }}</p>
                                                        </div>
                                                        {{-- <div class="card-footer p-0 border-0">
                                                            <div class="dropdown">
                                                                <a class="drop-btn" type="button"
                                                                    id="dropdownMenuButton" data-bs-toggle="dropdown"
                                                                    aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </a>
                                                                <ul class="dropdown-menu"
                                                                    aria-labelledby="dropdownMenuButton" style="">
                                                                    <li>
                                                                        <button
                                                                            class="dropdown-item complete fs-14 regular "
                                                                            type="button">
                                                                            <i
                                                                                class="bi bi-check-circle complete-icon"></i>
                                                                            Mark as Complete
                                                                        </button>
                                                                    </li>
                                                                    <li>
                                                                        <button class="dropdown-item cancel fs-14 regular"
                                                                            type="button">
                                                                            <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                            Cancel
                                                                        </button>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div> --}}
                                                    </div>
                                                </div>
                                            @empty
                                                <p class="fs-14 sora light-black normal service-details">No
                                                    Intro Cards Found</p>
                                            @endforelse
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- end business -->
                        @elseif(auth()->check() && auth()->user()->hasRole('customer'))
                            <!-- customer -->
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Personal Info</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal" data-bs-target="#personal-info-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-6 ">
                                                <label for="customer-full-name" class="form-label form-input-labels">Full
                                                    name</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter full-name" id="customer-full-name"
                                                    name="customer-full-name" value="{{ $user->name ?? '' }}" disabled>
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="customer-email" class="form-label form-input-labels">Email
                                                </label>
                                                <input type="email" class="form-control form-inputs-field"
                                                    placeholder="Enter email address" id="customer-email"
                                                    name="customer-email" value="{{ $user->email ?? '' }}" disabled>
                                            </div>

                                            <div class="col-md-12 ">
                                                <label for="customer-location"
                                                    class="form-label form-input-labels">Location</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter your location" id="customer-location"
                                                    name="customer-location" value="{{ $user->profle->email ?? '' }}"
                                                    disabled>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Service Preferences</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal"
                                            data-bs-target="#service-preferences-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <div class="d-flex gap-4 flex-wrap">
                                                    @forelse (Auth::user()->service_preferences as $service)
                                                        <p class="fs-14 sora light-black normal service-details">
                                                            {{ $service->name }}
                                                        </p>
                                                    @empty
                                                        <p class="fs-14 sora light-black normal service-details">No service
                                                            preferences added</p>
                                                    @endforelse
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Hair Type</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0 edit-body-spec-btn"
                                            data-type="hair">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                @php
                                                    $hairSpec = Auth::user()->getBodySpecificationByType('hair');
                                                @endphp
                                                @if ($hairSpec)
                                                    <div class="card flex-row gap-4 border-0 shadow-none">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="{{ $hairSpec->image ? asset('website' . '/' . $hairSpec->image) : asset('website/assets/images/wax.png') }}"
                                                                class="h-125px w-125px rounded-3 object-fit-contain"
                                                                alt="hair-type-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora black semi_bold">{{ $hairSpec->name }}
                                                            </p>
                                                            {!! $hairSpec->description ?? '' !!}
                                                        </div>
                                                    </div>
                                                @else
                                                    <p class="fs-15 black normal text-center">Hair type not added yet</p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Skin Type</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0 edit-body-spec-btn"
                                            data-type="skin">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                @php
                                                    $skinSpec = Auth::user()->getBodySpecificationByType('skin');
                                                @endphp
                                                @if ($skinSpec)
                                                    <div class="card flex-row gap-4 border-0 shadow-none">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="{{ $skinSpec->image ? asset('website' . '/' . $skinSpec->image) : asset('website/assets/images/wax.png') }}"
                                                                class="h-125px w-125px rounded-3 object-fit-contain"
                                                                alt="skin-type-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora black semi_bold">{{ $skinSpec->name }}
                                                            </p>
                                                            {!! $skinSpec->description ?? '' !!}
                                                        </div>
                                                    </div>
                                                @else
                                                    <p class="fs-15 black normal text-center">Skin type not added yet</p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Body Type</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0 edit-body-spec-btn"
                                            data-type="body">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                @php
                                                    $bodySpec = Auth::user()->getBodySpecificationByType('body');
                                                @endphp
                                                @if ($bodySpec)
                                                    <div class="card flex-row gap-4 border-0 shadow-none">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="{{ $bodySpec->image ? asset('website' . '/' . $bodySpec->image) : asset('website/assets/images/wax.png') }}"
                                                                class="h-125px w-125px rounded-3 object-fit-contain"
                                                                alt="body-type-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora black semi_bold">{{ $bodySpec->name }}
                                                            </p>
                                                            {!! $bodySpec->description ?? '' !!}
                                                        </div>
                                                    </div>
                                                @else
                                                    <p class="fs-15 black normal text-center">Body type not added yet</p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Allergies</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0 edit-body-spec-btn"
                                            data-type="allergy">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                @php
                                                    $allergySpec = Auth::user()->getBodySpecificationByType('allergy');
                                                @endphp
                                                @if ($allergySpec)
                                                    <div class="card flex-row gap-4 border-0 shadow-none">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="{{ $allergySpec->image ? asset('website' . '/' . $allergySpec->image) : asset('website/assets/images/wax.png') }}"
                                                                class="h-125px w-125px rounded-3 object-fit-contain"
                                                                alt="body-type-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora black semi_bold">{{ $allergySpec->name }}
                                                            </p>
                                                            {!! $allergySpec->description ?? '' !!}
                                                        </div>
                                                    </div>
                                                @else
                                                    <p class="fs-15 black normal text-center">Allergies not added yet</p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--  end customer -- -->
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    @include('dashboard.profile_settings.modal.edit-social-modal')
    @include('dashboard.profile_settings.modal.edit-personal-info-modal')
    @if(auth()->user()->hasRole('customer'))
    @include('dashboard.profile_settings.modal.body-specifications-modal')
    @include('dashboard.profile_settings.modal.service-preferences-modal')
    @else
    @include('dashboard.profile_settings.modal.edit-user-galleries-modal')
    @include('dashboard.profile_settings.modal.edit-company-details-modal')
    @include('dashboard.profile_settings.modal.edit-product-certifications-modal')
    @include('dashboard.profile_settings.modal.edit-certifications&licenses-modal')
    @include('dashboard.profile_settings.modal.edit-availability-modal')
    @include('dashboard.profile_settings.modal.intro-cards-modal')
    @endif
@endsection
@push('js')
    <!-- jQuery is already loaded in master layout -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script>
        $(document).ready(function() {
            $('.edit-body-spec-btn').on('click', function() {
                var type = $(this).data('type');
                $('#type').val(type);
                var title = type.charAt(0).toUpperCase() + type.slice(1) + ' Type';
                $('#body-modal-title').text(title);
                $('#body_name').val('');
                $('#description').val('');
                // Reset image input
                var imageInput = $('#body-specifications-modal .image-input[data-kt-image-input="true"]');
                imageInput.addClass('image-input-empty').removeClass('image-input-changed');
                imageInput.find('.image-input-wrapper').css('background-image', 'none');
                imageInput.find('[data-kt-image-input-action="remove"]').addClass('d-none');
                imageInput.find('[data-kt-image-input-action="cancel"]').addClass('d-none');
                $("#body-specifications-modal").modal('show');
                $.ajax({
                    url: '{{ route('body-specifications.get') }}',
                    type: 'GET',
                    data: {
                        type: type
                    },
                    success: function(response) {
                        if (response.success && response.data) {
                            var data = response.data;
                            $('#body_name').val(data.name);
                            // Set CKEditor content if it exists
                            if (window.bodySpecEditor) {
                                window.bodySpecEditor.setData(data.description || '');
                            } else {
                                $('#description').val(data.description || '');
                            }
                            if (data.image) {
                                var baseImageUrl = $('meta[name="asset-url"]').attr(
                                    'content') || '/website';
                                var imageUrl = baseImageUrl + '/' + data.image;
                                var wrapper = imageInput.find('.image-input-wrapper');
                                wrapper.css('background-image', 'url(' + imageUrl + ')');
                                imageInput.removeClass('image-input-empty').addClass(
                                    'image-input-changed');
                                imageInput.find('[data-kt-image-input-action="remove"]')
                                    .removeClass('d-none');
                                imageInput.find('[data-kt-image-input-action="cancel"]')
                                    .removeClass('d-none');
                                // Store original image for cancel functionality
                                imageInput.data('original-image', imageUrl);
                            } else {
                                // Clear original image data
                                imageInput.removeData('original-image');
                            }
                        }
                    },
                    error: function() {
                        // Modal is already shown, just keep it open with empty form
                        console.log('Error loading data, showing empty form');
                    }
                });
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            // Add custom validation method for CKEditor
            $.validator.addMethod("ckeditorRequired", function(value, element) {
                if (window.bodySpecEditor) {
                    var editorData = window.bodySpecEditor.getData();
                    return editorData.length > 0;
                }
                return value.length > 0;
            }, "Please enter description");

            $("#body-specifications-form").validate({
                rules: {
                    image: {
                        required: true
                    },
                    type: {
                        required: true
                    },
                    name: {
                        required: true
                    },
                    description: {
                        ckeditorRequired: true
                    },
                },
                messages: {
                    image: {
                        required: "Please upload an image"
                    },
                    type: {
                        required: "Please select type"
                    },
                    name: {
                        required: "Please enter name"
                    },
                    description: {
                        required: "Please enter description"
                    },
                },
                submitHandler: function(form) {
                    // Update textarea with CKEditor content before submitting
                    if (window.bodySpecEditor) {
                        const editorData = window.bodySpecEditor.getData();
                        $('#description').val(editorData);
                    }
                    form.submit();
                },
            });

            $('#service-preferences-form').validate({
                rules: {
                    'services[]': {
                        required: true
                    }
                },
                messages: {
                    'services[]': {
                        required: "Please select at least one service"
                    }
                },
                errorElement: 'div',
                errorClass: 'text-danger fw-bold',
                errorPlacement: function(error, element) {
                    if (element.attr('name') === 'services[]') {
                        error.insertAfter(element.next('.select2-container'));
                    } else {
                        error.insertAfter(element);
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                }
            });
        });
    </script>
    <script>
        // Initialize CKEditor for body specifications description
        $(document).ready(function() {
            $('#body-specifications-modal').on('shown.bs.modal', function() {
                if (!window.bodySpecEditor) {
                    ClassicEditor
                        .create(document.querySelector('#description'), {
                            toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList',
                                'numberedList', '|', 'outdent', 'indent', '|', 'blockQuote',
                                'insertTable', '|', 'undo', 'redo'
                            ]
                        })
                        .then(editor => {
                            window.bodySpecEditor = editor;
                        })
                        .catch(error => {
                            console.error('CKEditor initialization error:', error);
                        });
                }
            });

            // Clean up CKEditor when modal is hidden
            $('#body-specifications-modal').on('hidden.bs.modal', function() {
                if (window.bodySpecEditor) {
                    window.bodySpecEditor.destroy()
                        .then(() => {
                            window.bodySpecEditor = null;
                        })
                        .catch(error => {
                            console.error('CKEditor cleanup error:', error);
                        });
                }
            });

            // Update form validation to work with CKEditor
            $('#body-specifications-form').on('submit', function(e) {
                if (window.bodySpecEditor) {
                    // Get CKEditor content and set it to the textarea
                    const editorData = window.bodySpecEditor.getData();
                    $('#description').val(editorData);
                }
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            let socialIndex = $('.social-block').length;

            // Add More Social Block
            $('.add-social-btn').on('click', function() {
                const newSocialBlock = `
            <div class="social-block mb-4" data-index="${socialIndex}">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">Social ${socialIndex + 1}</h5>
                    <button type="button" class="btn btn-danger btn-sm delete-social-btn">Delete Block</button>
                </div>
                <div class="row row-gap-3">
                    <div class="col-md-12">
                        <label for="social-image-${socialIndex}" class="form-label form-input-labels">Social Logo</label>
                        <div class="position-relative form-add-services">
                            <div class="image-input image-input-empty image-input-circle" data-kt-image-input="true"
                                style="background-image: url({{ asset('website/images/image_input_holder.png') }})">
                                <div class="image-input-wrapper w-125px h-125px">
                                </div>
                                <label
                                    class="image-button btn btn-icon ms-6 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px shadow"
                                    data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                    data-bs-dismiss="click" title="Change avatar">
                                    <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                            class="path2"></span></i>
                                    <input type="file" name="socials[${socialIndex}][image]" accept=".png, .jpg, .jpeg" />
                                    <input type="hidden" name="socials[${socialIndex}][image_remove]" />
                                </label>
                                <span
                                    class="btn btn-icon ms-4 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                    data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                    data-bs-dismiss="click" title="Cancel avatar">
                                    <i class="ki-outline ki-cross fs-3"></i>
                                </span>
                                <span
                                    class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                    data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                    data-bs-dismiss="click" title="Remove avatar">
                                    <i class="ki-outline ki-cross fs-3"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="social-name-${socialIndex}" class="form-label form-input-labels">Social Name</label>
                        <input type="text" class="form-control form-inputs-field" placeholder="Enter social name"
                            id="social-name-${socialIndex}" name="socials[${socialIndex}][name]" value="">
                    </div>
                    <div class="col-md-6">
                        <label for="social-link-${socialIndex}" class="form-label form-input-labels">Social Link</label>
                        <input type="text" class="form-control form-inputs-field" placeholder="Enter social link"
                            id="social-link-${socialIndex}" name="socials[${socialIndex}][link]" value="">
                    </div>
                </div>
            </div>
        `;

                $('#social-blocks-container').append(newSocialBlock);
                socialIndex++;

                // Reinitialize image input for the new block
                KTImageInput.createInstances();
            });

            // Delete Social Block
            $(document).on('click', '.delete-social-btn', function() {
                $(this).closest('.social-block').remove();
                reindexSocialBlocks();
            });

            // Reindex social blocks after deletion
            function reindexSocialBlocks() {
                $('.social-block').each(function(index) {
                    $(this).attr('data-index', index);
                    $(this).find('h5').text('Social ' + (index + 1));

                    // Update all input names and IDs
                    $(this).find('input[type="file"]').attr('name', 'socials[' + index + '][image]');
                    $(this).find('input[type="hidden"][name*="image_remove"]').attr('name', 'socials[' +
                        index + '][image_remove]');
                    $(this).find('input[name*="[name]"]').attr('name', 'socials[' + index + '][name]').attr(
                        'id', 'social-name-' + index);
                    $(this).find('input[name*="[link]"]').attr('name', 'socials[' + index + '][link]').attr(
                        'id', 'social-link-' + index);
                    $(this).find('label[for*="social-image"]').attr('for', 'social-image-' + index);
                    $(this).find('label[for*="social-name"]').attr('for', 'social-name-' + index);
                    $(this).find('label[for*="social-link"]').attr('for', 'social-link-' + index);
                });

                socialIndex = $('.social-block').length;
            }
        });
    </script>
    <script>
        $(document).ready(function() {
            let galleryIndex = $('.gallery-block').length;

            // Add More Gallery Block
            $('.add-gallery-btn').on('click', function() {
                const newGalleryBlock = `
            <div class="gallery-block mb-4" data-index="${galleryIndex}">
                <div class="row align-items-center">
                    <div class="col-md-10">
                        <label for="gallery-image-${galleryIndex}" class="form-label form-input-labels">Image ${galleryIndex + 1}</label>
                        <div class="position-relative form-add-services">
                            <div class="image-input image-input-empty image-input-circle" data-kt-image-input="true"
                                style="background-image: url('/website/assets/images/image_input_holder.png')">
                                <div class="image-input-wrapper w-125px h-125px">
                                </div>
                                <label
                                    class="image-button btn btn-icon ms-6 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px shadow"
                                    data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                    data-bs-dismiss="click" title="Change image">
                                    <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                            class="path2"></span></i>
                                    <input type="file" name="galleries[${galleryIndex}][image]" accept=".png, .jpg, .jpeg" />
                                </label>
                                <span
                                    class="btn btn-icon ms-4 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                    data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                    data-bs-dismiss="click" title="Cancel image">
                                    <i class="ki-outline ki-cross fs-3"></i>
                                </span>
                                <span
                                    class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                    data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                    data-bs-dismiss="click" title="Remove image">
                                    <i class="ki-outline ki-cross fs-3"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-danger btn-sm delete-gallery-block-btn">
                            Delete
                        </button>
                    </div>
                </div>
            </div>
        `;

                $('#gallery-blocks-container').append(newGalleryBlock);
                galleryIndex++;

                // Show delete button for all blocks when there's more than one
                updateDeleteButtonVisibility();

                // Reinitialize image input for the new block
                KTImageInput.createInstances();
            });

            // Delete Gallery Block
            $(document).on('click', '.delete-gallery-block-btn', function() {
                $(this).closest('.gallery-block').remove();
                reindexGalleryBlocks();
                updateDeleteButtonVisibility();
            });

            // Handle image removal (clear old_image when removing)
            $(document).on('click', '[data-kt-image-input-action="remove"]', function() {
                const galleryBlock = $(this).closest('.gallery-block');
                const oldImageInput = galleryBlock.find('input[name*="[old_image]"]');
                if (oldImageInput.length) {
                    oldImageInput.val(''); // Clear the old image value
                }
            });

            // Reindex gallery blocks after deletion
            function reindexGalleryBlocks() {
                $('.gallery-block').each(function(index) {
                    $(this).attr('data-index', index);

                    // Update all input names, IDs, and labels
                    $(this).find('input[type="file"]').attr('name', 'galleries[' + index + '][image]');
                    $(this).find('input[name*="[old_image]"]').attr('name', 'galleries[' + index +
                        '][old_image]');
                    $(this).find('label[for*="gallery-image"]').attr('for', 'gallery-image-' + index).text(
                        'Image ' + (index + 1));
                });

                galleryIndex = $('.gallery-block').length;
            }

            // Update delete button visibility
            function updateDeleteButtonVisibility() {
                const galleryBlocks = $('.gallery-block');

                galleryBlocks.each(function(index) {
                    const deleteBtn = $(this).find('.delete-gallery-block-btn');

                    if (galleryBlocks.length === 1) {
                        // Hide delete button when there's only one block
                        deleteBtn.addClass('d-none');
                    } else {
                        // For multiple blocks, show delete button only on blocks after the first one
                        if (index === 0) {
                            deleteBtn.addClass('d-none'); // Keep first block's delete button hidden
                        } else {
                            deleteBtn.removeClass('d-none'); // Show delete button on additional blocks
                        }
                    }
                });
            }

            // Initialize delete button visibility on page load
            updateDeleteButtonVisibility();

            // Also update when modal is shown
            $('#edit-user-galleries-modal').on('shown.bs.modal', function() {
                updateDeleteButtonVisibility();
            });
        });

        // Certifications & Licenses Modal JavaScript
        let certIndex = $('#certifications-wrapper .file-upload-group').length - 1;

        function initFileUpload($group) {
            const allowedImages = ["image/png", "image/jpeg", "image/jpg"];
            const $dropArea = $group.find(".upload-box");
            const $fileInput = $group.find("input[type='file']");
            const $previewArea = $group.find(".preview-container");

            $dropArea.off();
            $fileInput.off();

            $dropArea.on("click", function() {
                $fileInput.click();
            });

            $fileInput.on("change", function(e) {
                const file = e.target.files[0];
                if (file) handleFile(file);
            });

            $dropArea.on("dragover", function(e) {
                e.preventDefault();
                $dropArea.css("background", "#eee");
            });

            $dropArea.on("dragleave", function(e) {
                e.preventDefault();
                $dropArea.css("background", "");
            });

            $dropArea.on("drop", function(e) {
                e.preventDefault();
                $dropArea.css("background", "");
                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    handleFile(files[0]);
                }
            });

            function handleFile(file) {
                if (!allowedImages.includes(file.type)) {
                    alert("Only PNG, JPEG, and JPG files are allowed.");
                    return;
                }

                if (file.size > 2 * 1024 * 1024) {
                    alert("File size must be less than 2MB.");
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    $previewArea.html(`
                        <div class="preview-item">
                            <img src="${e.target.result}" alt="Preview" style="max-width: 100px; max-height: 100px;">
                            <button type="button" class="remove-preview">×</button>
                        </div>
                    `);
                };
                reader.readAsDataURL(file);

                // Set the file to the input
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                $fileInput[0].files = dataTransfer.files;
            }

            // Remove preview
            $previewArea.on('click', '.remove-preview', function() {
                $previewArea.empty();
                $fileInput.val('');
            });
        }

        function updateDeleteButtonVisibility() {
            const $blocks = $('#certifications-wrapper .file-upload-group, .gray-card');
            $blocks.each(function(index) {
                const $deleteBtn = $(this).find('.delete-block');
                if ($blocks.length <= 1) {
                    $deleteBtn.hide();
                } else {
                    $deleteBtn.show();
                }
            });
        }

        function reindexRows() {
            $('#certifications-wrapper .file-upload-group, .gray-card').each(function(index) {
                $(this).find('input[name*="certificates["]').each(function() {
                    const name = $(this).attr('name');
                    if (name) {
                        const newName = name.replace(/certificates\[\d+\]/, `certificates[${index}]`);
                        $(this).attr('name', newName);
                    }
                });
                $(this).find('textarea[name*="certificates["]').each(function() {
                    const name = $(this).attr('name');
                    if (name) {
                        const newName = name.replace(/certificates\[\d+\]/, `certificates[${index}]`);
                        $(this).attr('name', newName);
                    }
                });
                $(this).find('textarea[id^="w3review_"]').attr('id', `w3review_${index}`);
                $(this).find('label[for^="w3review_"]').attr('for', `w3review_${index}`);
                $(this).find('h4').text(`Certificate #${index + 1}`);
            });
            certIndex = $('#certifications-wrapper .file-upload-group').length - 1;
        }

        function addCertificationBlock() {
            certIndex++;
            const wrapper = document.getElementById('certifications-wrapper');
            const totalCerts = $('#certifications-wrapper .file-upload-group').length + 1;

            const newBlock = `
                <div class="gray-card my-5 file-upload-group">
                    <h4 class="mb-8">Certificate #${totalCerts}</h4>
                    <div class="col-md-12">
                        <label class="fieldlabels">Certification Title*</label>
                        <input type="text" name="certificates[${certIndex}][title]" placeholder="Enter certification title">
                        <label class="fieldlabels">Issued by*</label>
                        <input class="no_validate" type="text" name="certificates[${certIndex}][issued_by]" placeholder="Enter name">
                    </div>
                    <div class="col-md-6">
                        <label class="fieldlabels">Issued Date*</label>
                        <input class="no_validate" type="date" name="certificates[${certIndex}][issued_date]" placeholder="Enter issued date">
                    </div>
                    <div class="col-md-6">
                        <label class="fieldlabels">End Date*</label>
                        <input class="no_validate" type="date" name="certificates[${certIndex}][end_date]" placeholder="Enter end date">
                    </div>
                    <div class="col-md-12 form-border">
                        <p class="manrope fw-600 light-black">Share Certificates</p>
                        <div class="file-upload-group">
                            <label class="upload-box">
                                <img src="/website/assets/images/upload.svg" alt="Upload Icon">
                                <p>Upload Certificate</p>
                                <p class="mb-0">Maximum file size: 2 MB</p>
                                <p>Supported format: JPG and PNG</p>
                                <span class="add-file">
                                    <p class="upload-cert-btn no_validate">Upload</p>
                                </span>
                                <input type="file" name="certificates[${certIndex}][image]" class="file-input no_validate" hidden="">
                            </label>
                            <div class="preview-container"></div>
                        </div>
                        <div class="exception-checkbox">
                            <label class="cert-excep">
                                <input class="no_validate" type="checkbox" name="certificates[${certIndex}][exception]" id="exceptionToggle_${certIndex}">
                                <span class="checkmark">Certificate Exception</span>
                            </label>
                            <div class="exception-textarea">
                                <label class="mb-2" for="w3review_${certIndex}">Reason for Exception</label>
                                <textarea class="mb-0 no_validate" id="w3review_${certIndex}" name="certificates[${certIndex}][exception_reason]" rows="4" cols="50" placeholder="Write reason for exception"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3 d-flex justify-content-between">
                        <button type="button" class="delete-block">Delete This Block</button>
                    </div>
                </div>
            `;

            wrapper.insertAdjacentHTML('beforeend', newBlock);

            const $newGroup = $('.file-upload-group').last();
            initFileUpload($newGroup);

            $newGroup.find('.delete-block').on('click', function() {
                $newGroup.remove();
                reindexRows();
                updateDeleteButtonVisibility();
            });

            updateDeleteButtonVisibility();
        }

        // Initialize certifications modal
        $(document).ready(function() {
            $('.file-upload-group').each(function() {
                initFileUpload($(this));
            });

            $(document).on('click', '.delete-block', function() {
                const $group = $(this).closest('.file-upload-group');
                const $grayCard = $(this).closest('.gray-card');

                if ($group.length > 0) {
                    $group.remove();
                } else if ($grayCard.length > 0) {
                    $grayCard.remove();
                }

                reindexRows();
                updateDeleteButtonVisibility();
            });

            $(document).on("dragover drop", function(e) {
                e.preventDefault();
            });

            // Exception checkbox toggle
            $(document).on('change', 'input[name*="[exception]"]', function() {
                const $textarea = $(this).closest('.exception-checkbox').find('.exception-textarea');
                if ($(this).is(':checked')) {
                    $textarea.show();
                } else {
                    $textarea.hide();
                    $textarea.find('textarea').val('');
                }
            });

            // Initialize exception textarea visibility on page load
            $('input[name*="[exception]"]').each(function() {
                const $textarea = $(this).closest('.exception-checkbox').find('.exception-textarea');
                if ($(this).is(':checked')) {
                    $textarea.show();
                } else {
                    $textarea.hide();
                }
            });

            updateDeleteButtonVisibility();
        });

        // Add More button for certifications
        $(document).on('click', '#addMoreBtn', function() {
            addCertificationBlock();
        });

        // Availability Modal JavaScript
        $(document).ready(function() {
            let holidayIndex = $('.custom-holiday-div').length;

            // Initialize flatpickr for existing time inputs in modals only
            $('#edit-availability-modal .flatpickr-time').each(function() {
                if (!this._flatpickr) {
                    flatpickr(this, {
                        enableTime: true,
                        noCalendar: true,
                        dateFormat: "H:i",
                        time_24hr: true
                    });
                }
            });

            // Day checkbox toggle functionality
            $(document).on('change', '.time-picker-calendar2 input[type="checkbox"]', function() {
                const $timeRange = $(this).closest('.time-picker-calendar2').find('.time-picker-range2');
                const $checkedTime = $timeRange.find('.checked-time');
                const $closedTime = $timeRange.find('.closed-time');

                if ($(this).is(':checked')) {
                    $checkedTime.show();
                    $closedTime.hide();

                    // Initialize flatpickr for time inputs
                    $checkedTime.find('.flatpickr-time').each(function() {
                        if (!this._flatpickr) {
                            flatpickr(this, {
                                enableTime: true,
                                noCalendar: true,
                                dateFormat: "H:i",
                                time_24hr: true
                            });
                        }
                    });
                } else {
                    $checkedTime.hide();
                    $closedTime.show();

                    // Clear time values
                    $checkedTime.find('.flatpickr-time').val('');
                }
            });

            // Holiday checkbox toggle functionality
            $(document).on('change', '.day-checkbox', function() {
                const $timeContainer = $(this).closest('.time-picker-calendar').find('.start-time');

                if ($(this).is(':checked')) {
                    $timeContainer.show();
                    $timeContainer.find('.flatpickr-time').each(function() {
                        if (!this._flatpickr) {
                            flatpickr(this, {
                                enableTime: true,
                                noCalendar: true,
                                dateFormat: "H:i",
                                time_24hr: true
                            });
                        }
                    });
                } else {
                    $timeContainer.hide();
                    $timeContainer.find('.flatpickr-time').val('');
                }
            });

            // Select All holidays functionality
            $(document).on('change', '.select-all', function() {
                const isChecked = $(this).is(':checked');
                $('.day-checkbox').prop('checked', isChecked).trigger('change');
            });

            // Custom holiday modal
            $(document).on('click', '.add-custom-holiday-btn', function() {
                $('#customHolidayModal').show();
            });

            $(document).on('click', '.close', function() {
                $('#customHolidayModal').hide();
            });

            // Save custom holiday
            $(document).on('click', '#saveCustomHoliday', function() {
                const name = $('#customHolidayName').val().trim();
                const date = $('#customHolidayDate').val().trim();

                if (name && date) {
                    const newHoliday = `
                        <div class="custom-holiday-div">
                            <div class="time-picker-calendar">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label class="days">
                                        <input type="checkbox" name="holidays[${holidayIndex}][holiday_id]" value="custom_${holidayIndex}" class="day-checkbox" checked>
                                        <span class="checkmark">${name}</span>
                                        <input type="hidden" name="custom_holidays[${holidayIndex}][name]" value="${name}">
                                        <input type="hidden" name="custom_holidays[${holidayIndex}][date]" value="${date}">
                                    </label>
                                    <p>${date}</p>
                                </div>
                                <div class="start-time" style="display: flex;">
                                    <div class="d-flex gap-10 justify-content-center">
                                        <input type="text" class="flatpickr-time no_validate" name="custom_holidays[${holidayIndex}][start_time]" placeholder="Start Time">
                                        <p class="mb-0"> - </p>
                                        <input type="text" class="flatpickr-time no_validate" name="custom_holidays[${holidayIndex}][end_time]" placeholder="End Time">
                                    </div>
                                    <button type="button" class="delete-holiday">Delete</button>
                                </div>
                            </div>
                        </div>
                    `;

                    $('.add-custom-holiday-btn').before(newHoliday);
                    holidayIndex++;

                    // Initialize flatpickr for new time inputs
                    $('.custom-holiday-div').last().find('.flatpickr-time').each(function() {
                        flatpickr(this, {
                            enableTime: true,
                            noCalendar: true,
                            dateFormat: "H:i",
                            time_24hr: true
                        });
                    });

                    $('#customHolidayName').val('');
                    $('#customHolidayDate').val('');
                    $('#customHolidayModal').hide();
                } else {
                    alert('Please fill in both holiday name and date.');
                }
            });

            // Delete custom holiday
            $(document).on('click', '.delete-holiday', function() {
                $(this).closest('.custom-holiday-div').remove();
            });

            // Initialize flatpickr for custom holiday date picker
            flatpickr('#edit-availability-modal #customHolidayDate', {
                dateFormat: "F j"
            });

            // Initialize existing state
            $('.time-picker-calendar2 input[type="checkbox"]').each(function() {
                const $timeRange = $(this).closest('.time-picker-calendar2').find('.time-picker-range2');
                const $checkedTime = $timeRange.find('.checked-time');
                const $closedTime = $timeRange.find('.closed-time');

                if ($(this).is(':checked')) {
                    $checkedTime.show();
                    $closedTime.hide();
                } else {
                    $checkedTime.hide();
                    $closedTime.show();
                }
            });

            // Initialize existing holiday checkboxes state
            $('.day-checkbox').each(function() {
                const $timeContainer = $(this).closest('.time-picker-calendar').find('.start-time');
                if (!$(this).is(':checked')) {
                    $timeContainer.hide();
                }
            });
        });

        // Intro Cards Modal JavaScript
        $(document).ready(function() {
            let cardIndex = {{ auth()->user()->introCards ? auth()->user()->introCards->count() : 1 }};

            // Add More Intro Card functionality
            $('#add-more-intro-card').on('click', function() {
                const newCardHtml = `
                    <div class="gray-card my-5 intro-card-block" data-index="${cardIndex}">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="mb-0">Card #${cardIndex + 1}</h4>
                            <button type="button" class="btn btn-sm btn-danger delete-intro-card">
                                <i class="bi bi-trash"></i> Delete
                            </button>
                        </div>
                        <div class="row row-gap-4">
                            <div class="col-md-12">
                                <label for="intro-image-${cardIndex}" class="form-label form-input-labels">Image</label>
                                <div class="position-relative form-add-services">
                                    <div class="image-input image-input-empty image-input-circle"
                                        data-kt-image-input="true"
                                        style="background-image: url('{{ asset('website/assets/images/image_input_holder.png') }}')">
                                        <div class="image-input-wrapper w-125px h-125px"></div>
                                        <label class="image-button btn btn-icon ms-6 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px shadow"
                                            data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                            data-bs-dismiss="click" title="Change image">
                                            <i class="ki-duotone ki-pencil fs-6">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                            <input type="file" name="introCards[${cardIndex}][image]" accept=".png, .jpg, .jpeg" />
                                            <input type="hidden" name="avatar_remove" />
                                        </label>
                                        <span class="btn btn-icon ms-4 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                            data-bs-dismiss="click" title="Cancel">
                                            <i class="ki-outline ki-cross fs-3"></i>
                                        </span>
                                        <span class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                            data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                            data-bs-dismiss="click" title="Remove">
                                            <i class="ki-outline ki-cross fs-3"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <label for="intro-heading-${cardIndex}" class="form-label form-input-labels">Heading</label>
                                <input type="text" class="form-control form-inputs-field"
                                    placeholder="Enter Heading" id="intro-heading-${cardIndex}"
                                    name="introCards[${cardIndex}][heading]">
                            </div>
                            <div class="col-md-12">
                                <label for="intro-description-${cardIndex}" class="form-label form-input-labels">Description</label>
                                <textarea class="form-control form-inputs-field" rows="3"
                                    placeholder="Enter description" id="intro-description-${cardIndex}"
                                    name="introCards[${cardIndex}][description]"></textarea>
                            </div>
                        </div>
                    </div>
                `;

                $('#intro-cards-wrapper').append(newCardHtml);

                // Initialize image input for the new card
                if (typeof KTImageInput !== 'undefined') {
                    KTImageInput.createInstances();
                }

                cardIndex++;
                updateIntroCardNumbers();
            });

            // Delete Intro Card functionality using event delegation
            $(document).on('click', '.delete-intro-card', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Check if there's more than one card before deleting
                const totalCards = $('.intro-card-block').length;

                if (totalCards <= 1) {
                    alert('You must have at least one intro card.');
                    return;
                }

                // Remove the card
                $(this).closest('.intro-card-block').remove();

                // Update card numbers and delete buttons
                updateIntroCardNumbers();
            });

            // Update card numbers after deletion
            function updateIntroCardNumbers() {
                $('.intro-card-block').each(function(index) {
                    const $card = $(this);

                    // Update card title
                    $card.find('h4').text(`Card #${index + 1}`);
                    $card.attr('data-index', index);

                    // Handle delete button visibility - first card should not have delete button
                    const $deleteBtn = $card.find('.delete-intro-card');
                    if (index === 0) {
                        $deleteBtn.remove(); // Remove delete button from first card
                    } else if ($deleteBtn.length === 0) {
                        // Add delete button if it doesn't exist and it's not the first card
                        const deleteButtonHtml = `
                            <button type="button" class="btn btn-sm btn-danger delete-intro-card">
                                <i class="bi bi-trash"></i> Delete
                            </button>
                        `;
                        $card.find('.d-flex.justify-content-between.align-items-center.mb-3').append(deleteButtonHtml);
                    }

                    // Update form field names and IDs
                    $card.find('input, textarea').each(function() {
                        const $input = $(this);

                        // Update name attribute
                        if ($input.attr('name') && $input.attr('name').includes('introCards[')) {
                            const newName = $input.attr('name').replace(/introCards\[\d+\]/, `introCards[${index}]`);
                            $input.attr('name', newName);
                        }

                        // Update id attribute
                        if ($input.attr('id') && $input.attr('id').includes('intro-')) {
                            const newId = $input.attr('id').replace(/-\d+$/, `-${index}`);
                            $input.attr('id', newId);
                        }
                    });

                    // Update labels
                    $card.find('label[for]').each(function() {
                        const $label = $(this);
                        if ($label.attr('for') && $label.attr('for').includes('intro-')) {
                            const newFor = $label.attr('for').replace(/-\d+$/, `-${index}`);
                            $label.attr('for', newFor);
                        }
                    });
                });
            }
        });
    </script>
@endpush
