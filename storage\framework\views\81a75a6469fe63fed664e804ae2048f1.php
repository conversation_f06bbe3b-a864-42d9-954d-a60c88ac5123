<?php $__env->startPush('css'); ?>

<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="service padding">
        <div class="container">
            <div class="row row-gap-10">
                <div class="col-md-12 d-flex justify-content-between">
                    <h4 class="sora black">Professional</h4>

                    <div class="search-bar d-flex align-items-center">
                        <i class="fa-solid fa-magnifying-glass me-3"></i>
                        <input type="text" placeholder="Search">
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="position-relative">
                        <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">

                            <!-- Swiper -->
                            <div class="swiper mySwiper professional-swipper">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active professional-tab" id="pills-home-tab"
                                                data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab"
                                                aria-controls="pills-home" aria-selected="true">Personal Trainers
                                            </button>
                                        </li>
                                    </div>
                                    <div class="swiper-slide">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link professional-tab" id="pills-profile-tab"
                                                data-bs-toggle="pill" data-bs-target="#pills-profile" type="button"
                                                role="tab" aria-controls="pills-profile" aria-selected="false">
                                                Makeup
                                                Artists
                                            </button>
                                        </li>
                                    </div>
                                    <div class="swiper-slide">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link professional-tab" id="pills-contact-tab"
                                                data-bs-toggle="pill" data-bs-target="#pills-contact" type="button"
                                                role="tab" aria-controls="pills-contact" aria-selected="false">Nail
                                                Technicians
                                            </button>
                                        </li>
                                    </div>
                                    <div class="swiper-slide">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link professional-tab" id="pills-contact-tab"
                                                data-bs-toggle="pill" data-bs-target="#massage-therapists" type="button"
                                                role="tab" aria-controls="Massage-Therapists" aria-selected="false">
                                                Massage
                                                Therapists
                                            </button>
                                        </li>
                                    </div>
                                    <div class="swiper-slide">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link professional-tab" id="pills-contact-tab"
                                                data-bs-toggle="pill" data-bs-target="#massage-therapists" type="button"
                                                role="tab" aria-controls="Massage-Therapists" aria-selected="false">
                                                Beauty
                                                Consultants
                                            </button>
                                        </li>
                                    </div>
                                    <div class="swiper-slide">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link professional-tab" id="pills-contact-tab"
                                                data-bs-toggle="pill" data-bs-target="#massage-therapists" type="button"
                                                role="tab" aria-controls="Massage-Therapists" aria-selected="false">
                                                Wellness
                                                Coaches
                                            </button>
                                        </li>
                                    </div>
                                    <div class="swiper-slide">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link professional-tab" id="pills-contact-tab"
                                                data-bs-toggle="pill" data-bs-target="#massage-therapists" type="button"
                                                role="tab" aria-controls="Massage-Therapists"
                                                aria-selected="false">Dietitians &
                                                Nutritionists
                                            </button>
                                        </li>
                                    </div>
                                    <div class="swiper-slide">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link professional-tab" id="pills-contact-tab"
                                                data-bs-toggle="pill" data-bs-target="#massage-therapists" type="button"
                                                role="tab" aria-controls="Massage-Therapists" aria-selected="false">Yoga
                                            </button>
                                        </li>
                                    </div>
                                    <div class="swiper-slide">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link professional-tab" id="pills-contact-tab"
                                                data-bs-toggle="pill" data-bs-target="#massage-therapists" type="button"
                                                role="tab" aria-controls="Massage-Therapists" aria-selected="false">
                                                Massage
                                                Therapists
                                            </button>
                                        </li>
                                    </div>
                                </div>
                                <div class="swiper-button-next"></div>
                                <div class="swiper-button-prev"></div>
                            </div>
                        </ul>
                    </div>
                    
                        
                        
                            
                            
                                
                                
                                
                            
                        
                    
                    <div class="tab-content mt-10" id="pills-tabContent  ">
                        <?php
                            $images = [
                                'card-image.png',
                                'professional1.png',
                                'professional2.png',
                                'card-image.png',
                                'professional1.png',
                                'professional2.png',
                                'card-image.png',
                                'professional1.png',
                                'professional2.png',
                                'card-image.png',
                                'professional1.png',
                                'professional2.png',
                            ];
                        ?>
                        <div class="tab-pane fade show active" id="pills-home" role="tabpanel"
                            aria-labelledby="pills-home-tab" tabindex="0">
                            <ul class="nav nav-pills mb-10  service-subcategory" id="pills-tab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active service-tab" id="pills-fitness-tab" data-bs-toggle="pill"
                                        data-bs-target="#pills-fitness" type="button" role="tab"
                                        aria-controls="pills-fitness" aria-selected="true">General Fitness
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link  service-tab" id="pills-strengthconditioning-tab"
                                        data-bs-toggle="pill" data-bs-target="#pills-strengthconditioning" type="button"
                                        role="tab" aria-controls="pills-strengthconditioning" aria-selected="true">
                                        Strength/Conditioning
                                    </button>
                                </li>
                            </ul>
                            <div class="d-flex justify-content-between align-items-center mb-10 mt-15">
                                <p class="m-0 fs-14 light-black">25 Personal Trainers near you</p>
                                <a href="" class="fs-14 sora normal black" data-bs-toggle="modal"
                                    data-bs-target="#filterModal">
                                    <div class="filter-select d-flex gap-2 align-items-center">
                                        <?php echo $__env->make('svg.filter', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                        <span>Filter</span>
                                    </div>
                                </a>
                            </div>
                            <div class="tab-content mt-10" id="pills-tabContent  ">
                                <div class="tab-pane fade active show" id="pills-fitness" role="tabpanel"
                                    aria-labelledby="pills-fitness-tab" tabindex="0">
                                    <div class="row row-gap-8">
                                        <?php if(auth()->guard()->check()): ?>
                                            <?php echo $__env->make('website.template.top-rated', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                                        <?php else: ?>
                                            <?php for($i = 0; $i < 4; $i++): ?>
                                                <div class="col-lg-3 col-sm-6 col-12">
                                                    <div class="card top-rated-card">
                                                        <div class="card-header border-0 p-0 position-relative">
                                                            <img src="<?php echo e(asset('website/assets/images/' . $images[$i])); ?>"
                                                                class="h-100 w-100 top-rated-image" alt="card-image">
                                                            <div class="fav-icon position-absolute  bottom-10 ">
                                                                <i class="fa-regular fa-heart"></i>
                                                                <input type="hidden" name="wishlist_product_id" value="123">
                                                            </div>
                                                            <div class="rated-div position-absolute">
                                                                <p class="fs-12 sora semi_bold m-0"><?php echo $__env->make('svg.rated', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>TOP RATED
                                                                </p>
                                                            </div>
                                                        </div>
                                                        <div class="card-body pb-0 p-5">
                                                            <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                                                            <p class="fs-15 sora bold m-0 light-black">4.5 <i
                                                                    class="fa-solid fa-star review-icon mx-1"></i> <span
                                                                    class="normal">(440)</span></p>
                                                            <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                                                        </div>
                                                        <div class="card-footer border-0 pt-0 p-5">
                                                            <span class="badge white-badge">Beauty Salon</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endfor; ?>

                                        <?php endif; ?>
                                        <?php for($i = 0; $i < 8; $i++): ?>
                                            <div class="col-lg-3 col-sm-6 col-12">
                                                <div class="card top-rated-card">
                                                    <div class="card-header border-0 p-0 position-relative">
                                                        <img src="<?php echo e(asset('website/assets/images/' . $images[$i])); ?>"
                                                            class="h-100 w-100 top-rated-image" alt="card-image">
                                                        <div class="fav-icon position-absolute  bottom-10 ">
                                                            <i class="fa-regular fa-heart"></i>
                                                            <input type="hidden" name="wishlist_product_id" value="123">
                                                        </div>

                                                    </div>
                                                    <div class="card-body pb-0 p-5">
                                                        <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                                                        <p class="fs-15 sora bold m-0 light-black">4.5 <i
                                                                class="fa-solid fa-star review-icon mx-1"></i> <span
                                                                class="normal">(440)</span></p>
                                                        <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                                                    </div>
                                                    <div class="card-footer border-0 pt-0 p-5">
                                                        <span class="badge white-badge">Beauty Salon</span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="pills-strengthconditioning" role="tabpanel"
                                    aria-labelledby="pills-strengthconditioning-tab" tabindex="0">
                                    <div class="row row-gap-8">
                                        <?php if(auth()->guard()->check()): ?>
                                            <?php echo $__env->make('website.template.top-rated', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                                        <?php else: ?>
                                            <?php for($i = 0; $i < 4; $i++): ?>
                                                <div class="col-lg-3 col-sm-6 col-12">
                                                    <div class="card top-rated-card">
                                                        <div class="card-header border-0 p-0 position-relative">
                                                            <img src="<?php echo e(asset('website/assets/images/' . $images[$i])); ?>"
                                                                class="h-100 w-100 top-rated-image" alt="card-image">
                                                            <div class="fav-icon position-absolute  bottom-10 ">
                                                                <i class="fa-regular fa-heart"></i>
                                                                <input type="hidden" name="wishlist_product_id" value="123">
                                                            </div>
                                                            <div class="rated-div position-absolute">
                                                                <p class="fs-12 sora semi_bold m-0"><?php echo $__env->make('svg.rated', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>TOP RATED
                                                                </p>
                                                            </div>
                                                        </div>
                                                        <div class="card-body pb-0 p-5">
                                                            <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                                                            <p class="fs-15 sora bold m-0 light-black">4.5 <i
                                                                    class="fa-solid fa-star review-icon mx-1"></i> <span
                                                                    class="normal">(440)</span></p>
                                                            <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                                                        </div>
                                                        <div class="card-footer border-0 pt-0 p-5">
                                                            <span class="badge white-badge">Beauty Salon</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endfor; ?>

                                        <?php endif; ?>
                                        <?php for($i = 0; $i < 8; $i++): ?>
                                            <div class="col-lg-3 col-sm-6 col-12">
                                                <div class="card top-rated-card">
                                                    <div class="card-header border-0 p-0 position-relative">
                                                        <img src="<?php echo e(asset('website/assets/images/' . $images[$i])); ?>"
                                                            class="h-100 w-100 top-rated-image" alt="card-image">
                                                        <div class="fav-icon position-absolute  bottom-10 ">
                                                            <i class="fa-regular fa-heart"></i>
                                                            <input type="hidden" name="wishlist_product_id" value="123">
                                                        </div>

                                                    </div>
                                                    <div class="card-body pb-0 p-5">
                                                        <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                                                        <p class="fs-15 sora bold m-0 light-black">4.5 <i
                                                                class="fa-solid fa-star review-icon mx-1"></i> <span
                                                                class="normal">(440)</span></p>
                                                        <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                                                    </div>
                                                    <div class="card-footer border-0 pt-0 p-5">
                                                        <span class="badge white-badge">Beauty Salon</span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab"
                            tabindex="0">
                            <div class="row row-gap-8">
                                <?php for($i = 0; $i < 4; $i++): ?>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="card top-rated-card">
                                            <div class="card-header border-0 p-0 position-relative">
                                                <img src="<?php echo e(asset('website/assets/images/' . $images[$i])); ?>"
                                                    class="h-100 w-100 top-rated-image" alt="card-image">
                                                <div class="fav-icon position-absolute  bottom-10 ">
                                                     <i class="fa-regular fa-heart"></i>
                                                         <input type="hidden" name="wishlist_product_id" value="123">
                                                </div>
                                                <div class="rated-div position-absolute">
                                                    <p class="fs-12 sora semi_bold m-0"><?php echo $__env->make('svg.rated', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>TOP RATED
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="card-body pb-0 p-5">
                                                <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                                                <p class="fs-15 sora bold m-0 light-black">4.5 <i
                                                        class="fa-solid fa-star review-icon mx-1"></i> <span
                                                        class="normal">(440)</span></p>
                                                <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                                            </div>
                                            <div class="card-footer border-0 pt-0 p-5">
                                                <span class="badge white-badge">Beauty Salon</span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endfor; ?>
                                <?php for($i = 0; $i < 8; $i++): ?>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="card top-rated-card">
                                            <div class="card-header border-0 p-0 position-relative">
                                                <img src="<?php echo e(asset('website/assets/images/' . $images[$i])); ?>"
                                                    class="h-100 w-100 top-rated-image" alt="card-image">
                                                <div class="fav-icon position-absolute  bottom-10 ">
                                                     <i class="fa-regular fa-heart"></i>
                                                         <input type="hidden" name="wishlist_product_id" value="123">
                                                </div>

                                            </div>
                                            <div class="card-body pb-0 p-5">
                                                <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                                                <p class="fs-15 sora bold m-0 light-black">4.5 <i
                                                        class="fa-solid fa-star review-icon mx-1"></i> <span
                                                        class="normal">(440)</span></p>
                                                <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                                            </div>
                                            <div class="card-footer border-0 pt-0 p-5">
                                                <span class="badge white-badge">Beauty Salon</span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-contact" role="tabpanel" aria-labelledby="pills-contact-tab"
                            tabindex="0">
                            <div class="row row-gap-8">
                                <?php for($i = 0; $i < 4; $i++): ?>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="card top-rated-card">
                                            <div class="card-header border-0 p-0 position-relative">
                                                <img src="<?php echo e(asset('website/assets/images/' . $images[$i])); ?>"
                                                    class="h-100 w-100 top-rated-image" alt="card-image">
                                                 <div class="fav-icon position-absolute  bottom-10 ">
                                                     <i class="fa-regular fa-heart"></i>
                                                         <input type="hidden" name="wishlist_product_id" value="123">
                                                </div>
                                                <div class="rated-div position-absolute">
                                                    <p class="fs-12 sora semi_bold m-0"><?php echo $__env->make('svg.rated', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>TOP RATED
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="card-body pb-0 p-5">
                                                <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                                                <p class="fs-15 sora bold m-0 light-black">4.5 <i
                                                        class="fa-solid fa-star review-icon mx-1"></i> <span
                                                        class="normal">(440)</span></p>
                                                <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                                            </div>
                                            <div class="card-footer border-0 pt-0 p-5">
                                                <span class="badge white-badge">Beauty Salon</span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endfor; ?>
                                <?php for($i = 0; $i < 8; $i++): ?>
                                    <div class="col-lg-3 col-sm-6 col-12">
                                        <div class="card top-rated-card">
                                            <div class="card-header border-0 p-0 position-relative">
                                                <img src="<?php echo e(asset('website/assets/images/' . $images[$i])); ?>"
                                                    class="h-100 w-100 top-rated-image" alt="card-image">
                                                <div class="fav-icon position-absolute  bottom-10 ">
                                                     <i class="fa-regular fa-heart"></i>
                                                         <input type="hidden" name="wishlist_product_id" value="123">
                                                </div>

                                            </div>
                                            <div class="card-body pb-0 p-5">
                                                <p class="fs-16 semi_bold black m-0 ">Beauty Loft Salon Fz Lcc</p>
                                                <p class="fs-15 sora bold m-0 light-black">4.5 <i
                                                        class="fa-solid fa-star review-icon mx-1"></i> <span
                                                        class="normal">(440)</span></p>
                                                <p class="fs-14 regular light-black">Great Falls, Maryland</p>
                                            </div>
                                            <div class="card-footer border-0 pt-0 p-5">
                                                <span class="badge white-badge">Beauty Salon</span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

    </section>
    <?php echo $__env->make('website.template.modal.filter-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\git-file\anders\resources\views/website/professional.blade.php ENDPATH**/ ?>