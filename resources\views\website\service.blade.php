@extends('website.layout.master')
@section('content')
    <section class="service padding">
        <div class="container">
            <div class="row row-gap-10">
                <div class="col-md-12 d-flex justify-content-between">
                    <h4 class="sora black">Services</h4>
                    <div class="search-bar d-flex align-items-center">
                        <i class="fa-solid fa-magnifying-glass me-3"></i>
                        <input class="search-servies" type="text" placeholder="Search" name="search-servies">
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="position-relative">
                        <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">

                            <!-- Swiper -->
                            <div class="swiper mySwiper professional-swipper ">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link  professional-tab" id="pills-home-tab"
                                                data-bs-toggle="pill" data-bs-target="#pills-home" type="button"
                                                role="tab" aria-controls="pills-home" aria-selected="true">Personal
                                                Trainers
                                            </button>
                                        </li>
                                    </div>
                                    @foreach ($categories as $category)
                                        <div class="swiper-slide">
                                            <li class="nav-item" role="presentation">
                                                <button
                                                    class="nav-link {{ $category->slug == $active_category ? 'active' : '' }} professional-tab category-tab-btn"
                                                    id="pills-profile-tab" data-category-name="{{ $category->slug }}"
                                                    data-bs-toggle="pill" data-bs-target="#pills-profile" type="button"
                                                    role="tab" aria-controls="pills-profile" aria-selected="false"
                                                    href="#services-makeup-artist-subcategory-1"> {{ $category->name }}
                                                </button>
                                            </li>
                                        </div>
                                    @endforeach
                                </div>
                                <div class="swiper-button-next"></div>
                                <div class="swiper-button-prev"></div>
                            </div>
                        </ul>
                    </div>
                    <hr class="mt-0 mb-2">

                    <div id="subcategory-service-container"></div>
                </div>
            </div>
        </div>
    </section>

    @include('website.template.modal.filter-modal')
@endsection

@push('js')
    <script>
        $(document).ready(function() {
            $('.category-tab-btn').on('click', function() {
                let category = $(this).data("category-name");
                let subcategory = $(this).data("subcategory-name");

                // Update the browser's URL without reloading the page
                let newUrl = `/services-all/${category}`;
                if (subcategory) {
                    newUrl += `/${subcategory}`;
                }
                window.history.pushState({
                    path: newUrl
                }, '', newUrl);


                $.ajax({
                    url: "{{ route('filter_services') }}",
                    type: "GET",
                    data: {
                        category: category,
                        subcategory: subcategory
                    },
                    success: function(response) {
                        if(response.status == true){
                            $('#subcategory-service-container').html(response.data);
                        }else{
                            $('#subcategory-service-container').html('Something went wrong');
                        }
                    }
                });
                // $('.service-subcategory .nav-link').removeClass('active');
                // $(this).addClass('active');
            });
        });
    </script>
@endpush
