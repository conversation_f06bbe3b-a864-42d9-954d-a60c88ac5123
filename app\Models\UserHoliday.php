<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserHoliday extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'holiday_id',
        'name',
        'date',
        'start_time',
        'end_time',
        'is_full_day',
        'is_custom',
    ];

    public function detailHoliday()
    {
        return $this->belongsTo(Holiday::class);
    }
}
