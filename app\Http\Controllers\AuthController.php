<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Certification;
use App\Models\Holiday;
use App\Models\Profile;
use App\Models\ProfessionalRegistrationProgress;
use App\Models\Service;
use App\Models\User;
use App\Models\UserCertificate;
use App\Models\UserHoliday;
use App\Models\UserOpeningHour;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    function set_password(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|min:8',
            'scenario' => 'sometimes|in:login,register', // Optional parameter to indicate scenario
            'user_type' => 'sometimes|in:customer,professional', // User type for role validation
        ]);
        if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }

        $user = User::where('email', $request->email)->first();

        if ($user) {
            // User exists - this is a login scenario
            if (!Hash::check($request->password, $user->password)) {
                return api_response(false, "Invalid password");
            }

            // Check if user has the correct role for the selected user type
            $userRole = $user->getRoleNames()->first();
            $selectedUserType = $request->user_type;
            if (in_array($userRole, ["individual", "business", "professional"]) || $selectedUserType == "professional") {
            } elseif ($userRole == "customer" && $selectedUserType == "customer") {
            } else {
                return api_response(false, "This email is registered as a {$userRole}. Please select the correct account type or use a different email.");
            }

            if ($user->registration_completed == 1) {
                // Check if user is approved
                if ($user->approval == 0) {
                    return api_response(false, "Your account is not yet approved. Please wait for approval.");
                }

                // Check if user is active
                if ($user->status == 0) {
                    return api_response(false, "Your account is not active. Please contact support.");
                }
            }


            // Login the user
            Auth::login($user);

            return response()->json([
                'status' => true,
                'message' => "Login successful",
                'role' => $userRole,
                'scenario' => 'login'
            ]);
        } else {
            // User doesn't exist - this should not happen in normal flow
            // But we'll handle it as registration scenario
            return api_response(false, "User not found. Please complete email verification first.");
        }
    }

    function registerUserType($user_type)
    {
        if (auth()->user()->registration_completed == 1) {
            return redirect()->route('dashboard');
        }
        $role = auth()->user()->roles->first()->name ?? null;
        if ($role && $role == $user_type) {
            $user = auth()->user();
            if ($user->hasRole("customer")) {
                if (auth()->user()->profile) {
                    return redirect()->route('dashboard');
                }
            }
            $categories = Category::where("status", 1)->get();
            $services = Service::where("status", 1)->get();
            $certifications = Certification::all();
            $holidays = Holiday::where("status", 1)->get();
            return view("auth.register." . $user_type, compact("categories", "certifications", 'holidays', 'services'));
        } else {
            abort(403, 'Unauthorized action.');
        }
    }

    function registerCustomer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'fullname' => 'required',
            'password' => 'required|min:8',
            'phone' => 'required',
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->with([
                    'error' => 'Validation Error',
                    'message' => $validator->errors()->first(),
                    'type' => 'error',
                ])
                ->withInput();
        }
        $user = User::find(auth()->id());
        $user->name = $request->fullname;
        $user->password = Hash::make($request->password);
        $user->registration_completed = 1;
        $user->save();
        $profile = $user->profile;
        if ($profile == null) {
            $profile = new Profile();
            $profile->phone = $request->phone;
            if (isset($request->avatar)) {
                $profile->pic = $this->storeImage("user-image", $request->file('avatar'));
            } else {
                $profile->pic = 'no_avatar.jpg';
            }
            $profile->save();
        }
        $profile->user_id = $user->id;
        $profile->save();
        if ($request->has('services')) {
            $user->service_preferences()->sync($request->services);
        }
        return redirect("/")->with([
            'title' => 'Done',
            'message' => 'You have been registered successfully',
            'type' => 'success',
        ]);
    }

    /**
     * Save step data for professional registration
     */
    public function saveStepData(Request $request)
    {
        $user = auth()->user();
        $step = $request->input('step');
        // Get or create progress record
        $progress = ProfessionalRegistrationProgress::firstOrCreate(
            ['user_id' => $user->id],
            ['current_step' => 1]
        );

        // Handle file uploads first
        $stepData = $this->handleFileUploads($request, $step);

        // Validate and save step data
        $validatedData = $this->validateStepData($request, $step);
        if (isset($validatedData["error"])) {
            return response()->json(['success' => false, 'message' => $validatedData["error"]]);
        }

        // For step 5, process the banner image immediately to avoid storing large base64 data
        if ($step == 5 && isset($validatedData['banner_image']) && !empty($validatedData['banner_image'])) {
            try {
                $validatedData['banner_image'] = $this->handleCroppedImage($validatedData['banner_image']); // Store only filename, not base64
            } catch (\Exception $e) {
                Log::error('Failed to process banner image: ' . $e->getMessage());
                return response()->json(['success' => false, 'message' => 'Failed to process banner image']);
            }
        }

        // Merge file upload data with validated data
        $stepData = array_merge($validatedData, $stepData);

        // Save step data with error handling for large data
        try {
            $progress->setStepData($step, $stepData);
        } catch (\Exception $e) {
            Log::error('Failed to save step data: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to save step data. Please try again.']);
        }

        // Also save to profile for immediate use
        $this->saveToProfile($user, $step, $stepData);

        // If this is step 5 (final step), return success for AJAX and let frontend handle redirect
        if ($step == 5) {
            return response()->json([
                'success' => true,
                'message' => 'Registration completed successfully',
                'redirect' => route('dashboard')
            ]);
        }
        return response()->json(['success' => true, 'current_step' => $progress->current_step]);
    }

    /**
     * Get current progress for professional registration
     */
    public function getProgress(Request $request)
    {
        $user = auth()->user();
        $progress = ProfessionalRegistrationProgress::where('user_id', $user->id)->first();
        if ($progress) {
            return response()->json([
                'success' => true,
                'current_step' => $progress->current_step,
                'completed_steps' => $progress->getCompletedStepsCount()
            ]);
        }
        return response()->json(['success' => true, 'current_step' => 1]);
    }

    /**
     * Validate step data based on step number
     */
    private function validateStepData(Request $request, $step)
    {
        $rules = $this->getValidationRules($step);
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return ['error' => $validator->errors()->first()];
            // return false;
        }
        return $request->only(array_keys($rules));
    }

    /**
     * Get validation rules for each step
     */
    private function getValidationRules($step)
    {
        switch ($step) {
            case 1:
                return [
                    'avatar' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
                    'full_name' => 'required|string|max:255',
                    'company_name' => 'required|string|max:255',
                    'phone' => 'required|string|max:20',
                    'website' => 'nullable|url',
                    'facebook' => 'nullable|url',
                    'instagram' => 'nullable|url',
                    'tiktok' => 'nullable|url',
                    'location' => 'required|string',
                    // 'lat' => 'required|numeric',
                    // 'lng' => 'required|numeric',
                    'location_service' => 'required|integer|min:1',
                    'company_id' => 'required|string',
                    'vat_number' => 'required|string',
                ];
            case 2:
                return [
                    'subcategories' => 'required|array',
                    // 'category' => 'required|array',
                    // 'category.*.id' => 'required|integer',
                    // 'category.*.subcategory' => 'nullable|array',
                    // 'category.*.subcategory.*' => 'integer',
                    // 'category.*.subcategory' => 'required_if:category.*.id,true|array',
                ];
            case 3:
                return [
                    'product_certifications' => 'nullable|array',
                    'certificates' => 'nullable|array',
                    // 'certificates.*.title' => 'required|string',
                    // 'certificates.*.issued_by' => 'required|string',
                    // 'certificates.*.issued_date' => 'required|date',
                    // 'certificates.*.end_date' => 'required|date',
                    // 'certificates.*.image' => 'nullable|mimes:jpeg,png,jpg|max:2048',
                    // 'certificates.*.exception' => 'nullable|string',
                    // "certificates.*.exception_reason" => 'nullable|string|required_if:certificates.*.exception,1',
                ];
            case 4:
                return [
                    'availability' => 'required|array',
                    'holidays' => 'nullable|array',
                    'custom_holidays' => 'nullable|array',
                ];
            case 5:
                return [
                    'banner_image' => 'required|string',
                    // 'gallery_images' => 'nullable|array',
                ];
            default:
                return [];
        }
    }

    /**
     * Save step data to user profile
     */
    private function saveToProfile($user, $step, $data)
    {
        $profile = $user->profile ?? new Profile();
        $profile->user_id = $user->id;
        $user = User::find(auth()->id());

        switch ($step) {
            case 1:
                $user->name = $data['full_name'] ?? $user->name;
                // $profile->name = $data['full_name'] ?? // $profile->;
                $profile->pic =  $data['avatar'] ?? $profile->pic;
                $profile->company_name = $data['company_name'] ?? $profile->company_name;
                $profile->phone = $data['phone'] ?? $profile->phone;
                $profile->website = $data['website'] ?? $profile->website;
                $profile->facebook = $data['facebook'] ?? $profile->facebook;
                $profile->instagram = $data['instagram'] ?? $profile->instagram;
                $profile->tiktok = $data['tiktok'] ?? $profile->tiktok;
                $profile->location = $data['location'] ?? $profile->location;
                $profile->lat = $data['lat'] ?? $profile->lat;
                $profile->lng = $data['lng'] ?? $profile->lng;
                $profile->location_service = $data['location_service'] ?? $profile->location_service;
                $profile->company_id = $data['company_id'] ?? $profile->company_id;
                $profile->vat_number = $data['vat_number'] ?? $profile->vat_number;
                break;
            case 2:
                $subcategories = $data['subcategories'] ?? [];
                $user->subcategories()->sync($subcategories);
                break;
            case 3:
                $productCertifications = $data['product_certifications'] ?? [];
                $user->product_cerficates()->sync($productCertifications);
                $certifications = $data['certificates'] ?? [];
                UserCertificate::where('user_id', $user->id)->delete();
                foreach ($certifications as $certification) {
                    $userCertificate = new UserCertificate();
                    $userCertificate->user_id = $user->id;
                    $userCertificate->title = $certification['title'] ?? null;
                    $userCertificate->issued_by = $certification['issued_by'] ?? null;
                    $userCertificate->issued_date = $certification['issued_date'] ?? null;
                    $userCertificate->end_date = $certification['end_date'] ?? null;
                    if (isset($certification['image'])) {
                        $userCertificate->image = $this->storeImage('certificates', $certification['image']);
                    } elseif (isset($certification['old_image'])) {
                        $userCertificate->image = $certification['old_image'];
                    }
                    $userCertificate->exception = $certification['exception'] ?? null;
                    $userCertificate->exception_reason = $certification['exception_reason'] ?? null;
                    $userCertificate->save();
                }
                break;
            case 4:
                $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                $opening_hours = $data['availability'] ?? [];
                UserOpeningHour::where('user_id', $user->id)->delete();
                foreach ($days as $day) {
                    $hour = collect($opening_hours)->firstWhere('day', $day);

                    $userOpeningHour = new UserOpeningHour();
                    $userOpeningHour->user_id = $user->id;
                    $userOpeningHour->day = $day;

                    if ($hour) {
                        $userOpeningHour->open = $hour['start'] ?? null;
                        $userOpeningHour->close = $hour['end'] ?? null;
                        $userOpeningHour->type = "open";
                    } else {
                        $userOpeningHour->open = null;
                        $userOpeningHour->close = null;
                        $userOpeningHour->type = "close";
                    }
                    $userOpeningHour->save();
                }
                // open hour end here

                // Holiday start here

                $holidays = $data['holidays'] ?? [];
                if (!empty($holidays)) {
                    UserHoliday::where('user_id', $user->id)->delete();
                    foreach ($holidays as $holiday) {
                        if (isset($holiday['holiday_id'])) {
                            $exist_holiday = Holiday::where('id', $holiday['holiday_id'])->first();
                            if (!$exist_holiday) {
                                continue;
                            }
                            $userHoliday = new UserHoliday();
                            $userHoliday->user_id = $user->id;
                            $userHoliday->holiday_id = $exist_holiday->id;
                            $userHoliday->name = $holiday['name'] ?? null;
                            $userHoliday->date = $holiday['date'] ?? null;
                            $userHoliday->start_time = $holiday['start_time'] ?? null;
                            $userHoliday->end_time = $holiday['end_time'] ?? null;
                            if (isset($holiday['start_time'], $holiday['end_time'])) {
                                $userHoliday->is_full_day = 0;
                            } else {
                                $userHoliday->is_full_day = 1;
                            }
                            $userHoliday->is_custom = 0;
                            $userHoliday->save();
                        }
                    }
                }
                $customHolidays = $data['custom_holidays'] ?? [];
                if (!empty($customHolidays)) {
                    UserHoliday::where('user_id', $user->id)->where("is_custom", 1)->delete();
                    foreach ($customHolidays as $customHoliday) {
                        $rawDate = $customHoliday['date'] ?? null;

                        if ($rawDate) {
                            try {
                                $formattedDate = \Carbon\Carbon::parse($rawDate)->format('Y-m-d');

                                $exists = UserHoliday::where('user_id', $user->id)
                                    ->where('date', $formattedDate)
                                    ->exists();

                                if (!$exists) {
                                    $userHoliday = new UserHoliday();
                                    $userHoliday->user_id = $user->id;
                                    $userHoliday->name = $customHoliday['name'] ?? null;
                                    $userHoliday->date = $formattedDate;
                                    $userHoliday->start_time = $customHoliday['start_time'] ?? null;
                                    $userHoliday->end_time = $customHoliday['end_time'] ?? null;
                                    if (isset($customHoliday['start_time'], $customHoliday['end_time'])) {
                                        $userHoliday->is_full_day = 0;
                                    } else {
                                        $userHoliday->is_full_day = 1;
                                    }
                                    $userHoliday->is_custom = 1;
                                    $userHoliday->save();
                                }
                            } catch (\Exception $e) {
                                continue;
                            }
                        }
                    }
                }
                // Holiday end here
                break;
            case 5:
                $profile->banner_image = $data['banner_image'];
                $user->registration_completed = 1;
                break;
        }
        $user->save();
        $profile->save();
    }

    /**
     * Handle file uploads for each step
     */
    private function handleFileUploads(Request $request, $step)
    {
        $uploadedFiles = [];
        switch ($step) {
            case 1:
                if ($request->hasFile('avatar')) {
                    $uploadedFiles['avatar'] = $this->storeImage('user-image', $request->file('avatar'));
                }
                break;
        }
        return $uploadedFiles;
    }

    /**
     * Handle cropped image from base64
     */
    private function handleCroppedImage($base64Image)
    {
        if (empty($base64Image)) return null;

        // Decode base64 image
        $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64Image));
        $filename = 'user-image/' . 'cropped_' . time() . '.png';
        Storage::disk('website')->put($filename, $imageData);
        return $filename;
    }
}
