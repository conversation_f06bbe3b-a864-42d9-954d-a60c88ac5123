<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Home;
use App\Models\PrivacyAndTerm;
use App\Models\Service;
use App\Models\SubCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class WebsiteController extends Controller
{
    public function index()
    {
        $page = Home::with('details')->first();
        $categories = Category::where('status', 1)->get();
        $services = Service::active()->get();
        return view('website.index', compact('page', 'categories'));
    }
    public function services($category, $subcategory = null)
    {
        $categories = Category::active()->get();
        $active_category = $category;

        // Get the selected category with its subcategories
        $selectedCategory = Category::with('subcategories')->where('slug', $category)->active()->first();

        if (!$selectedCategory) {
            abort(404, 'Category not found');
        }

        // If no subcategory is provided or subcategory doesn't exist, use the first subcategory
        $activeSubcategory = null;
        if ($subcategory) {
            $activeSubcategory = $selectedCategory->subcategories->where('slug', $subcategory)->first();
        }

        // If subcategory not found or not provided, use first subcategory
        if (!$activeSubcategory && $selectedCategory->subcategories->count() > 0) {
            $activeSubcategory = $selectedCategory->subcategories->first();
            // Redirect to the correct URL with first subcategory
            return redirect()->route('website_services', [
                'category' => $category,
                'subcategory' => $activeSubcategory->slug
            ]);
        }

        return view('website.service', compact('categories', 'active_category', 'selectedCategory', 'activeSubcategory'));
    }

    public function filterServices(Request $request)
    {
        try {
            $category = Category::with(['subcategories' => function($query) {
                $query->where('status', 1);
            }])->where('slug', $request->category)->active()->first();

            if (!$category) {
                return api_response(false, "Category not found");
            }

            $subcategories = $category->subcategories;
            $activeSubcategory = null;

            // If subcategory is provided, find it, otherwise use first subcategory
            if ($request->subcategory) {
                $activeSubcategory = $subcategories->where('slug', $request->subcategory)->first();
            }

            if (!$activeSubcategory && $subcategories->count() > 0) {
                $activeSubcategory = $subcategories->first();
            }

            $data = view('website.template.subcategory-service', compact('category', 'subcategories', 'activeSubcategory'))->render();
            return api_response(true, "Services loaded", $data);

        } catch(\Exception $e) {
            return api_response(false, "Something went wrong");
        }
    }

    public function filterServicesBySubcategory(Request $request)
    {
        try {
            $subcategory = SubCategory::with(['services' => function($query) {
                $query->where('status', 1);
            }])->where('slug', $request->subcategory)->where('status', 1)->first();

            if (!$subcategory) {
                return api_response(false, "Subcategory not found");
            }

            $services = $subcategory->services;

            $data = view('website.template.services-list', compact('services'))->render();
            return api_response(true, "Services loaded", $data);

        } catch(\Exception $e) {
            return api_response(false, "Something went wrong");
        }
    }
    public function professional()
    {
        return view('website.professional');
    }
    public function privacyPolicy()
    {
        $policies = PrivacyAndTerm::where('type', 'privacy')->get();
        return view('website.privacy-policy', compact('policies'));
    }
    public function terms()
    {
        $terms = PrivacyAndTerm::where('type', 'term')->get();
        return view('website.term', compact('terms'));
    }

    public function clear_all()
    {
        Artisan::call('route:clear');
        Artisan::call('cache:clear');
        Artisan::call('optimize:clear');
        Artisan::call('view:clear');
        Artisan::call('config:clear');
        return '<div style="text-align:center;"> <h1 style="text-align:center;">Cache and Config and permission cache are cleared.</h1><h4><a href="/">Go to home</a></h4></div>';
    }
}
