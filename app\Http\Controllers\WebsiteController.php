<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Home;
use App\Models\PrivacyAndTerm;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class WebsiteController extends Controller
{
    public function index()
    {
        $page = Home::with('details')->first();
        $categories = Category::where('status', 1)->get();
        $services = Service::active()->get();
        return view('website.index', compact('page', 'categories'));
    }
    public function services($category, $subcategory = null)
    {
        $categories = Category::active()->get();
        $active_category = $category;
        $services = Service::get();
        return view('website.service', compact('services', 'categories', 'active_category'));
    }
    public function filterServices(Request $request)
    {
        // try{
            $categories = Category::with("subcategories","services")->where("slug", $request->category)->active()->first();
            $subcategories = $categories->subcategories;
            $services = $categories->services;
            $data = view('website.template.subcategory-service', compact('categories', 'subcategories', 'services'))->render();
            return api_response(true, "Services", $data);
        // }catch(\Exception $e){
        //     return api_response(false, "Something went wrong");
        // }
    }
    public function professional()
    {
        return view('website.professional');
    }
    public function privacyPolicy()
    {
        $policies = PrivacyAndTerm::where('type', 'privacy')->get();
        return view('website.privacy-policy', compact('policies'));
    }
    public function terms()
    {
        $terms = PrivacyAndTerm::where('type', 'term')->get();
        return view('website.term', compact('terms'));
    }

    public function clear_all()
    {
        Artisan::call('route:clear');
        Artisan::call('cache:clear');
        Artisan::call('optimize:clear');
        Artisan::call('view:clear');
        Artisan::call('config:clear');
        return '<div style="text-align:center;"> <h1 style="text-align:center;">Cache and Config and permission cache are cleared.</h1><h4><a href="/">Go to home</a></h4></div>';
    }
}
