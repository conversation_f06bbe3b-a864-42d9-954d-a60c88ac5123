
<div>
    <ul class="nav nav-pills service-subcategory mb-10" id="pills-tab" role="tablist">
        <?php $__currentLoopData = $subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?php echo e(($activeSubcategory && $activeSubcategory->id == $subcategory->id) || (!$activeSubcategory && $loop->first) ? 'active' : ''); ?> subcategory-tab-btn"
                    data-subcategory-slug="<?php echo e($subcategory->slug); ?>"
                    data-subcategory-name="<?php echo e($subcategory->name); ?>"
                    type="button">
                    <?php echo e($subcategory->name); ?>

                </button>
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>

    <div class="d-flex justify-content-between align-items-center mb-10 mt-15">
        <p class="m-0 fs-14 light-black" id="services-count">
            <span class="services-count-number">0</span> Services near you
        </p>
        <a href="" class="fs-14 sora normal black" data-bs-toggle="modal" data-bs-target="#filterModal">
            <div class="filter-select d-flex gap-2 align-items-center">
                <?php echo $__env->make('svg.filter', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <span>Filter</span>
            </div>
        </a>
    </div>
</div>

<hr>


<!-- Loading Spinner -->
<div id="services-loader" class="text-center py-5" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2 text-muted">Loading services...</p>
</div>

<!-- Services Container -->
<div id="services-container" class="mt-10">
    <?php if($activeSubcategory && $activeSubcategory->services): ?>
        <?php echo $__env->make('website.template.services-list', ['services' => $activeSubcategory->services], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php else: ?>
        <div class="row">
            <div class="col-12 text-center py-5">
                <div class="no-services-found">
                    <i class="fa-solid fa-search fs-48 text-muted mb-3"></i>
                    <h5 class="text-muted">Select a subcategory</h5>
                    <p class="text-muted">Please select a subcategory to view available services.</p>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php /**PATH D:\git-file\anders\resources\views/website/template/subcategory-service.blade.php ENDPATH**/ ?>