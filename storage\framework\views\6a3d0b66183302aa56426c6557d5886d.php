
<div>
    <ul class="nav nav-pills  service-subcategorymb-10" id="pills-tab" role="tablist">

        <?php $__currentLoopData = $subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?php echo e($loop->first ? 'active' : ''); ?> service-tab" id="pills-fitness-tab"
                    data-bs-toggle="pill" data-bs-target="#pills-fitness" type="button" role="tab"
                    aria-controls="pills-fitness" aria-selected="true"
                    href="#services-personal-trainer-subcategory-1"><?php echo e($subcategory->name); ?>

                </button>
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        
    </ul>
    <div class="d-flex justify-content-between align-items-center mb-10 mt-15">
        <p class="m-0 fs-14 light-black"><?php echo e($services->count()); ?> Services near you</p>
        <a href="" class="fs-14 sora normal black" data-bs-toggle="modal" data-bs-target="#filterModal">
            <div class="filter-select d-flex gap-2 align-items-center">
                <?php echo $__env->make('svg.filter', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <span>Filter</span>
            </div>
        </a>
    </div>
</div>

<hr>


<div class="tab-content mt-10" id="pills-tabContent  ">
    <?php
        $images = [
            'trainer1.png',
            'trainer2.png',
            'trainer3.png',
            'trainer4.png',
            'trainer5.png',
            'trainer6.png',
            'trainer7.png',
            'trainer8.png',
            'trainer9.png',
            'trainer10.png',
            'trainer11.png',
            'trainer5.png',
        ];
    ?>

    <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab"
        tabindex="0">


        <div class="tab-content mt-10" id="pills-tabContent  ">
            <div class="tab-pane fade active show" id="pills-fitness" role="tabpanel"
                aria-labelledby="pills-fitness-tab" tabindex="0">
                <div class="row row-gap-8">
                    <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $i = $loop->index;
                        ?>
                        <div class="col-md-3">
                            <div class="card top-rated-card services-card">
                                <div class="card-header border-0 p-0 ">
                                    <img src="<?php echo e(asset('website' . '/' . $service->image)); ?>"
                                        class="h-100 w-100 top-rated-image" alt="card-image">
                                </div>
                                <div class="card-body p-5 bg-white">
                                    <p class="fs-16 semi_bold black m-0 "><?php echo e($service->name); ?></p>
                                    <div class="d-flex gap-2 align-items-center">
                                        <img src="<?php echo e(asset('website') . '/' . ($service?->user?->profile?->avatar ?? '')); ?>"
                                            class="rounded-pill w-25px h-25px" alt="card-image">
                                        <div>
                                            <p class="fs-11 semi_bold black m-0">Marshals Gents
                                                Salon
                                            </p>
                                            <p class="fs-10px sora semi_bold m-0 light-black"><i
                                                    class="fa-solid fa-star"
                                                    style="color: #000000; font-size: 10px;"></i>5.0
                                                <span class="normal deep-blue ms-1">(546)</span>
                                                <span class="light-black opacity-6 ms-1">
                                                    <?php echo $__env->make('svg.dot', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?> Al Barsha South,
                                                    Dubai</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer border-0 d-flex justify-content-between p-5">
                                    <div>
                                        <p class="m-0 fs-16 black bold">$<?php echo e($service->price); ?></p>
                                        <p class="m-0 fs-14 regular "><i class="fa-regular fa-clock"></i>
                                            <?php echo e($service->duration); ?> mins</p>
                                    </div>
                                    <a href="" class="blue-button">Book Now</a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            
        </div>
    </div>

    
</div>
<?php /**PATH D:\git-file\anders\resources\views/website/template/subcategory-service.blade.php ENDPATH**/ ?>