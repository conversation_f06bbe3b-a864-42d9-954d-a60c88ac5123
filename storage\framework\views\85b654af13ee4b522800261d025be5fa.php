<?php $__env->startPush('css'); ?>
    <style>
        ..slider-container {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }


    </style>
<?php $__env->stopPush(); ?>

<!-- Filters Modal -->
<div class="modal fade filter-modal" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-5">
                <p class="modal-title fs-15 sora black semi_bold" id="filtersModalLabel">Filters</p>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <form class="">
                    <div class="row row-gap-5">
                        <div class="col-md-4">
                            <label class="form-label form-input-labels">Sort by</label>
                        </div>
                        <div class="col-md-12 d-flex gap-8 flex-wrap">

                            <div class="d-flex gap-2 flex-column">
                                <label class="d-flex gap-2 align-items-center">
                                    <input class="form-check-input" type="radio" name="sort" value="Recommended">
                                    <span>Recommended</span>
                                </label>
                                <label class="d-flex gap-2 align-items-center">
                                    <input class="form-check-input" type="radio" name="sort" value="Nearest"> <span>
                                        Nearest
                                    </span>
                                </label>
                                <label class="d-flex gap-2 align-items-center">
                                    <input class="form-check-input" type="radio" name="sort" value="Top-rated"> <span>
                                        Top-rated
                                    </span>
                                </label>
                            </div>
                            <div class="d-flex gap-2 flex-column">
                                <label class="d-flex gap-2 align-items-center">
                                    <input class="form-check-input" type="radio" name="sort" value="    Group Service">
                                    <span>
                                        Group Service
                                    </span>
                                </label>

                                <label class="d-flex gap-2 align-items-center">
                                    <input class="form-check-input" type="radio" name="sort" value=" Private"> <span>
                                        Private
                                    </span>
                                </label>
                                <label class="d-flex gap-2 align-items-center">
                                    <input class="form-check-input" type="radio" name="sort" value="Individual"> <span>
                                        Individual
                                    </span>
                                </label>
                            </div>



                        </div>
                        <div class="col-md-12">
                            <div class="d-flex justify-content-between">
                                <p class="form-label form-input-labels">Maximum price</p>
                                <p class="form-label form-input-labels normal" id="priceValue "> $250</p>
                            </div>

                            <div class="slider-container">
                                <input type="range" id="range-slider" min="0" max="100" value="0" class="slider">
                            </div>

                        </div>
                        <div class="col-md-12">
                            <!-- Category -->
                            <label class="form-label form-input-labels">Category</label>
                            <div class="d-flex flex-wrap gap-3">
                                <label class="category-checkbox">
                                    <input type="checkbox" name="category" value="General Fitness Trainers" checked>
                                    <span>General Fitness Trainers</span>
                                </label>
                                <label class="category-checkbox">
                                    <input type="checkbox" name="category" value="Strength and Conditioning Coaches">
                                    <span>Strength and Conditioning Coaches</span>
                                </label>
                                <label class="category-checkbox">
                                    <input type="checkbox" name="category" value="Weight Loss Specialists">
                                    <span>Weight Loss Specialists</span>
                                </label>
                                <label class="category-checkbox">
                                    <input type="checkbox" name="category" value="Functional Fitness Trainers">
                                    <span>Functional Fitness Trainers</span>
                                </label>
                            </div>

                        </div>
                    </div>
                </form>
            </div>

            <div class="modal-footer border-0 pt-0">
                <button type="button" class="trans-button " data-bs-dismiss="modal">Clear all</button>
                <button type="button" class="add-btn "  data-bs-dismiss="modal">Apply</button>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('js'); ?>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const slider = document.getElementById('range-slider');

            // Function to update the background color based on the thumb's value
            function updateSliderBackground() {
                const value = (slider.value - slider.min) / (slider.max - slider.min) * 100;
                slider.style.background = `linear-gradient(to right, #020C87 ${value}%, #F0F0F0 ${value}%)`;
            }

            // Update the background when the slider value changes
            slider.addEventListener('input', updateSliderBackground);

            // Set initial background when the page loads
            updateSliderBackground();
        });

    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\git-file\anders\resources\views/website/template/modal/filter-modal.blade.php ENDPATH**/ ?>