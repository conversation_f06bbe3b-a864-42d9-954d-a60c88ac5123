{{--
                    ///////////////////////
                        SUBCATEGORIES
                    ///////////////////////
--}}
<div>
    <ul class="nav nav-pills service-subcategory mb-10" id="pills-tab" role="tablist">
        @foreach ($subcategories as $subcategory)
            <li class="nav-item" role="presentation">
                <button class="nav-link {{ ($activeSubcategory && $activeSubcategory->id == $subcategory->id) || (!$activeSubcategory && $loop->first) ? 'active' : '' }} subcategory-tab-btn"
                    data-subcategory-slug="{{ $subcategory->slug }}"
                    data-subcategory-name="{{ $subcategory->name }}"
                    type="button">
                    {{ $subcategory->name }}
                </button>
            </li>
        @endforeach
    </ul>

    <div class="d-flex justify-content-between align-items-center mb-10 mt-15">
        <p class="m-0 fs-14 light-black" id="services-count">
            <span class="services-count-number">0</span> Services near you
        </p>
        <a href="" class="fs-14 sora normal black" data-bs-toggle="modal" data-bs-target="#filterModal">
            <div class="filter-select d-flex gap-2 align-items-center">
                @include('svg.filter')
                <span>Filter</span>
            </div>
        </a>
    </div>
</div>

<hr>
{{--
                    ///////////////////////
                        SERVICES
                    ///////////////////////
--}}

<!-- Loading Spinner -->
<div id="services-loader" class="text-center py-5" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2 text-muted">Loading services...</p>
</div>

<!-- Services Container -->
<div id="services-container" class="mt-10">
    @if($activeSubcategory && $activeSubcategory->services)
        @include('website.template.services-list', ['services' => $activeSubcategory->services])
    @else
        <div class="row">
            <div class="col-12 text-center py-5">
                <div class="no-services-found">
                    <i class="fa-solid fa-search fs-48 text-muted mb-3"></i>
                    <h5 class="text-muted">Select a subcategory</h5>
                    <p class="text-muted">Please select a subcategory to view available services.</p>
                </div>
            </div>
        </div>
    @endif
</div>

